# Google Cloud Storage Setup with gcloud CLI

## Overview
This application now uses Google Cloud Storage (GCS) instead of Supabase Storage for secure image handling. Users cannot access the real GCS URLs directly - all images are served through secure API endpoints.

## Features
- **Secure Image Serving**: Images are served through `/api/images/cover/[filename]` endpoints
- **URL Protection**: Real GCS URLs are hidden from clients
- **Automatic Signed URLs**: Temporary access URLs with expiration
- **API-based File Management**: Upload, delete, and list files through secure endpoints
- **gcloud CLI Integration**: Automated setup and deployment scripts

## Quick Setup with gcloud CLI

### 1. Prerequisites
Install and configure gcloud CLI:
```bash
# Install gcloud CLI (if not already installed)
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Login to Google Cloud
gcloud auth login
gcloud auth application-default login

# Set your project (optional)
gcloud config set project YOUR_PROJECT_ID
```

### 2. Automated Setup
Run the setup script to automatically configure GCS:
```bash
# Make the script executable
chmod +x scripts/setup-gcs.sh

# Run with default settings
./scripts/setup-gcs.sh

# Or with custom settings
./scripts/setup-gcs.sh -p my-project -b my-bucket -r us-west1
```

The script will:
- Create or verify Google Cloud project
- Enable required APIs (Storage, IAM)
- Create GCS bucket with proper permissions
- Create service account with necessary roles
- Generate service account key
- Update your `.env.local` file
- Test the configuration

### 3. Manual Setup (Alternative)
If you prefer manual setup:

1. Create a Google Cloud Project
2. Enable the Cloud Storage API
3. Create a storage bucket
4. Create a service account with Storage Admin permissions
5. Download the service account key JSON file

### 4. Environment Configuration
Update your `.env.local` file with:

```env
# Google Cloud Storage
GOOGLE_CLOUD_PROJECT_ID="your-project-id"
GOOGLE_CLOUD_BUCKET_NAME="your-bucket-name"
GOOGLE_CLOUD_KEY_FILE="path/to/service-account-key.json"

# Alternative for production (use credentials JSON string):
# GOOGLE_CLOUD_CREDENTIALS='{"type":"service_account","project_id":"your-project-id",...}'
```

### 3. Service Account Permissions
Your service account needs the following roles:
- Storage Admin (or Storage Object Admin)
- Storage Object Creator
- Storage Object Viewer

### 4. Bucket Configuration
- Make sure your bucket is in the correct region
- Set up appropriate lifecycle rules if needed
- Configure CORS if serving images directly (optional)

## API Endpoints

### Image Serving
- `GET /api/images/cover/[filename]` - Serves cover images with signed URLs
- `GET /api/images/chapter/[filename]` - Serves chapter images with signed URLs
- `DELETE /api/images/cover/[filename]` - Deletes specific cover images

### File Management
- `GET /api/media/files` - Lists all files in the bucket
- `POST /api/media/files` - Uploads new files
- `DELETE /api/media/files?filename=...` - Deletes files

### Upload Endpoints
- `POST /api/upload/cover` - Uploads cover images for novels
- `POST /api/upload/chapter-images` - Uploads images for chapter content
- `DELETE /api/upload/cover/[filename]` - Deletes cover images
- `DELETE /api/upload/chapter-images?filename=...&chapterId=...` - Deletes chapter images

## Security Features

### URL Protection
- Real GCS URLs are never exposed to clients
- All images served through secure API endpoints
- Signed URLs with automatic expiration (1 hour default)

### Authentication
- All upload/delete operations require AUTHOR role
- File listing requires authentication
- Image serving uses signed URLs for additional security

### File Validation
- File type validation
- Size limits
- Automatic filename generation with UUIDs

## File Structure
```
src/
├── lib/
│   └── gcs.ts                    # GCS utility functions
├── app/
│   └── api/
│       ├── images/
│       │   ├── cover/
│       │   │   └── [filename]/
│       │   │       └── route.ts  # Secure cover image serving
│       │   └── chapter/
│       │       └── [filename]/
│       │           └── route.ts  # Secure chapter image serving
│       ├── media/
│       │   └── files/
│       │       └── route.ts      # File management API
│       └── upload/
│           ├── cover/
│           │   ├── route.ts      # Cover upload endpoint
│           │   └── [filename]/
│           │       └── route.ts  # Cover delete endpoint
│           └── chapter-images/
│               └── route.ts      # Chapter image upload/delete endpoint
```

## Migration from Supabase Storage

The following files have been updated to use GCS:
- `src/lib/gcs.ts` - New GCS utility functions
- `src/app/api/upload/cover/route.ts` - Updated upload endpoint
- `src/app/api/upload/cover/[filename]/route.ts` - Updated delete endpoint
- `src/app/dashboard/media/page.tsx` - Updated media management UI
- `next.config.js` - Added GCS domain to image config

## Usage in Components

### Getting Image URLs
```typescript
import { getCoverImageUrl, getChapterImageUrl } from '@/lib/gcs'

// Cover images - returns a secure API endpoint, not the real GCS URL
const coverUrl = await getCoverImageUrl(filename)
// Returns: /api/images/cover/covers%2Ffile.jpg

// Chapter images - returns a secure API endpoint for chapter content images
const chapterImageUrl = await getChapterImageUrl(filename)
// Returns: /api/images/chapter/chapters%2Ffile.jpg
```

### Uploading Files
```typescript
import { uploadCoverImage, uploadChapterImage } from '@/lib/gcs'

// Upload cover image
const fileBuffer = Buffer.from(await file.arrayBuffer())
const coverResult = await uploadCoverImage(fileBuffer, file.name, file.type)

// Upload chapter image
const chapterResult = await uploadChapterImage(fileBuffer, file.name, file.type)
```

### Deleting Files
```typescript
import { deleteCoverImage, deleteChapterImage } from '@/lib/gcs'

// Delete cover image
const coverSuccess = await deleteCoverImage(filename)

// Delete chapter image
const chapterSuccess = await deleteChapterImage(filename)
```

## Chapter Image Integration

### Overview
Chapter images are stored in the `chapters/` folder within your GCS bucket and are served through secure API endpoints. Images can be embedded in chapter content using markdown syntax.

### Image Embedding in Chapters
Authors can embed images in chapter content using markdown syntax:
```markdown
![Image description](image_url)
```

### Folder Structure in GCS Bucket
```
your-bucket-name/
├── covers/           # Novel cover images
│   ├── uuid1.jpg
│   └── uuid2.png
└── chapters/         # Chapter content images
    ├── chapter-chapterId-timestamp.jpg
    └── chapter-chapterId-timestamp.png
```

### Security Features for Chapter Images
- **Author-only uploads**: Only authenticated authors can upload chapter images
- **Chapter ownership verification**: Authors can only upload images for their own chapters
- **Secure serving**: All images served through `/api/images/chapter/[filename]` endpoints
- **Automatic cleanup**: Images are associated with specific chapters for easier management

### Performance Optimizations
- **Lazy loading**: Chapter images load only when visible in the viewport
- **Responsive sizing**: Images automatically scale to fit reading width
- **Caching**: 2-hour signed URL expiry with proper cache headers
- **Click-to-zoom**: Full-size image viewing in modal dialogs

## Troubleshooting

### Common Issues
1. **Authentication Error**: Make sure your service account key is correct
2. **Bucket Not Found**: Verify the bucket name and project ID
3. **Permission Denied**: Check service account roles
4. **File Not Found**: Ensure the filename includes the correct folder prefix

### Environment Variables
- Use `GOOGLE_CLOUD_KEY_FILE` for local development
- Use `GOOGLE_CLOUD_CREDENTIALS` for production deployments
- Never commit service account keys to version control

## Production Deployment

### Using Service Account Key File
1. Upload your service account key to your server
2. Set `GOOGLE_CLOUD_KEY_FILE` to the file path
3. Ensure proper file permissions (600)

### Using Credentials JSON String
1. Convert your service account key to a JSON string
2. Set `GOOGLE_CLOUD_CREDENTIALS` environment variable
3. This is recommended for serverless deployments

## Deployment to Google Cloud

### Cloud Run Deployment
Deploy your application to Google Cloud Run:
```bash
# Make the deployment script executable
chmod +x scripts/deploy-gcloud.sh

# Deploy with default settings
./scripts/deploy-gcloud.sh

# Or with custom settings
./scripts/deploy-gcloud.sh -p my-project -r us-west1 -s my-service
```

The deployment script will:
- Enable required APIs (Cloud Build, Cloud Run, etc.)
- Create secrets in Secret Manager
- Build and deploy to Cloud Run
- Set up Cloud SQL database (optional)
- Configure environment variables

### Authentication Options

#### 1. Service Account Key (Local Development)
```env
GOOGLE_CLOUD_KEY_FILE="./gcs-service-account-key.json"
```

#### 2. Application Default Credentials (Recommended)
```bash
# For local development
gcloud auth application-default login

# For production (uses service account attached to compute resource)
# No additional configuration needed
```

#### 3. Credentials JSON String (Production)
```env
GOOGLE_CLOUD_CREDENTIALS='{"type":"service_account","project_id":"..."}'
```

### gcloud CLI Commands Reference

#### Project and Authentication
```bash
# List projects
gcloud projects list

# Set active project
gcloud config set project PROJECT_ID

# Login
gcloud auth login
gcloud auth application-default login

# Check authentication
gcloud auth list
```

#### Storage Operations
```bash
# List buckets
gsutil ls

# List files in bucket
gsutil ls gs://BUCKET_NAME

# Upload file
gsutil cp file.jpg gs://BUCKET_NAME/

# Download file
gsutil cp gs://BUCKET_NAME/file.jpg .

# Delete file
gsutil rm gs://BUCKET_NAME/file.jpg

# Set bucket permissions
gsutil iam ch user:<EMAIL>:objectViewer gs://BUCKET_NAME
```

#### Cloud Run Operations
```bash
# List services
gcloud run services list

# Deploy service
gcloud run deploy SERVICE_NAME --source .

# Get service URL
gcloud run services describe SERVICE_NAME --region=REGION --format="value(status.url)"

# Update service
gcloud run services update SERVICE_NAME --region=REGION

# Delete service
gcloud run services delete SERVICE_NAME --region=REGION
```

#### Secret Manager Operations
```bash
# Create secret
echo "secret-value" | gcloud secrets create SECRET_NAME --data-file=-

# Update secret
echo "new-secret-value" | gcloud secrets versions add SECRET_NAME --data-file=-

# Access secret
gcloud secrets versions access latest --secret=SECRET_NAME

# List secrets
gcloud secrets list
```

## Performance Considerations
- Signed URLs are cached for 1 hour
- Files are served with appropriate cache headers
- Consider implementing CDN for better performance
- Monitor GCS usage and costs
- Use Cloud Run with appropriate scaling settings

## Monitoring and Logging

### Cloud Monitoring
```bash
# Enable monitoring
gcloud services enable monitoring.googleapis.com

# View metrics
gcloud alpha monitoring dashboards list
```

### Cloud Logging
```bash
# View logs
gcloud logging read "resource.type=cloud_run_revision" --limit=50

# Create log sink
gcloud logging sinks create SINK_NAME DESTINATION
```

## Security Best Practices
- Use IAM roles with least privilege
- Regularly rotate service account keys
- Use Application Default Credentials in production
- Enable audit logging
- Set up proper CORS policies
- Use VPC for sensitive workloads
- Monitor access patterns