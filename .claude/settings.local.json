{"permissions": {"allow": ["Bash(pg_isready:*)", "Bash(supabase projects:*)", "Bash(supabase db:*)", "Bash(supabase status:*)", "Bash(supabase orgs:*)", "Bash(supabase link:*)", "Bash(npx prisma migrate:*)", "Bash(npx prisma:*)", "Bash(grep:*)", "Bash(DATABASE_URL=\"**********************************************************************************/postgres?pgbouncer=true\" DIRECT_URL=\"*****************************************************************************/postgres\" npx prisma db push)", "Bash(find:*)", "Bash(ls:*)", "Bash(rg:*)", "Bash(npm install:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(gcloud auth:*)", "Bash(gcloud config get-value:*)", "Bash(gcloud services enable:*)", "<PERSON><PERSON>(gsutil mb:*)", "Bash(gsutil ls:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}