"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { Upload, Image as ImageIcon, X, Copy, Check } from "lucide-react"
import { cn } from "@/lib/utils"
import Image from "next/image"

interface ImageUploadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  chapterId: string
  onImageInsert: (markdownText: string) => void
}

interface UploadedImage {
  filename: string
  url: string
  originalName: string
}

export function ImageUploadDialog({
  open,
  onOpenChange,
  chapterId,
  onImageInsert,
}: ImageUploadDialogProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedImage, setUploadedImage] = useState<UploadedImage | null>(null)
  const [altText, setAltText] = useState("")
  const [copiedMarkdown, setCopiedMarkdown] = useState(false)
  const { toast } = useToast()

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return

      const file = acceptedFiles[0]
      setIsUploading(true)

      try {
        const formData = new FormData()
        formData.append("file", file)
        formData.append("chapterId", chapterId)

        const response = await fetch("/api/upload/chapter-images", {
          method: "POST",
          body: formData,
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to upload image")
        }

        const result = await response.json()
        setUploadedImage(result.data)
        setAltText(result.data.originalName.replace(/\.[^/.]+$/, "")) // Remove extension for alt text

        toast({
          title: "Success",
          description: "Image uploaded successfully!",
        })
      } catch (error) {
        console.error("Upload error:", error)
        toast({
          title: "Upload Failed",
          description: error instanceof Error ? error.message : "Failed to upload image",
          variant: "destructive",
        })
      } finally {
        setIsUploading(false)
      }
    },
    [chapterId, toast]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpg", ".jpeg", ".png", ".webp"],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false,
    disabled: isUploading || !!uploadedImage,
  })

  const generateMarkdown = () => {
    if (!uploadedImage) return ""
    return `![${altText}](${uploadedImage.url})`
  }

  const handleInsertImage = () => {
    const markdown = generateMarkdown()
    onImageInsert(markdown)
    handleClose()
  }

  const handleCopyMarkdown = async () => {
    const markdown = generateMarkdown()
    try {
      await navigator.clipboard.writeText(markdown)
      setCopiedMarkdown(true)
      setTimeout(() => setCopiedMarkdown(false), 2000)
      toast({
        title: "Copied!",
        description: "Image markdown copied to clipboard",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  const handleClose = () => {
    setUploadedImage(null)
    setAltText("")
    setCopiedMarkdown(false)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Upload Chapter Image
          </DialogTitle>
          <DialogDescription>
            Upload an image to include in your chapter content.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {!uploadedImage ? (
            <div
              {...getRootProps()}
              className={cn(
                "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
                isDragActive
                  ? "border-primary bg-primary/5"
                  : "border-muted-foreground/25 hover:border-muted-foreground/50",
                isUploading && "opacity-50 cursor-not-allowed"
              )}
            >
              <input {...getInputProps()} />
              <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              {isUploading ? (
                <p className="text-sm text-muted-foreground">Uploading...</p>
              ) : isDragActive ? (
                <p className="text-sm text-muted-foreground">Drop the image here</p>
              ) : (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">
                    Drag & drop an image here, or click to select
                  </p>
                  <p className="text-xs text-muted-foreground">
                    JPG, PNG, WebP up to 10MB
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Image Preview */}
              <div className="relative">
                <Image
                  src={uploadedImage.url}
                  alt={altText}
                  width={400}
                  height={300}
                  className="w-full h-auto rounded-lg border"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={() => setUploadedImage(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Alt Text Input */}
              <div className="space-y-2">
                <Label htmlFor="alt-text">Alt Text (Description)</Label>
                <Input
                  id="alt-text"
                  value={altText}
                  onChange={(e) => setAltText(e.target.value)}
                  placeholder="Describe the image for accessibility"
                />
              </div>

              {/* Markdown Preview */}
              <div className="space-y-2">
                <Label>Markdown Code</Label>
                <div className="relative">
                  <Textarea
                    value={generateMarkdown()}
                    readOnly
                    className="font-mono text-sm"
                    rows={2}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={handleCopyMarkdown}
                  >
                    {copiedMarkdown ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          {uploadedImage && (
            <Button onClick={handleInsertImage}>
              Insert Image
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
