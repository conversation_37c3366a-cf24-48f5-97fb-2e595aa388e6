"use client"

import { useState, useCallback } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, ZoomIn } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog"

interface ChapterContentRendererProps {
  content: string
  fontSize?: number
  className?: string
}

interface ImageMatch {
  fullMatch: string
  alt: string
  src: string
  index: number
}

interface ChapterImageProps {
  src: string
  alt: string
  className?: string
}

function ChapterImage({ src, alt, className }: ChapterImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const handleLoad = useCallback(() => {
    setIsLoading(false)
    setHasError(false)
  }, [])

  const handleError = useCallback(() => {
    setIsLoading(false)
    setHasError(true)
  }, [])

  if (hasError) {
    return (
      <div className={cn(
        "flex items-center justify-center p-8 border border-dashed border-muted-foreground/25 rounded-lg bg-muted/50",
        className
      )}>
        <div className="text-center space-y-2">
          <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto" />
          <p className="text-sm text-muted-foreground">
            Failed to load image: {alt || "Untitled"}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("relative group my-6", className)}>
      {isLoading && (
        <Skeleton className="w-full h-64 rounded-lg" />
      )}
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <div className="relative cursor-pointer">
            <Image
              src={src}
              alt={alt}
              width={800}
              height={600}
              className={cn(
                "w-full h-auto rounded-lg shadow-sm transition-all duration-200",
                "hover:shadow-md hover:scale-[1.02]",
                isLoading ? "opacity-0" : "opacity-100"
              )}
              style={{ display: isLoading ? 'none' : 'block' }}
              onLoad={handleLoad}
              onError={handleError}
              priority={false}
              loading="lazy"
            />
            
            {/* Zoom overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
              <Button
                variant="secondary"
                size="sm"
                className="bg-white/90 hover:bg-white text-black"
              >
                <ZoomIn className="h-4 w-4 mr-2" />
                View Full Size
              </Button>
            </div>
          </div>
        </DialogTrigger>
        
        <DialogContent className="max-w-4xl w-full p-0">
          <div className="relative">
            <Image
              src={src}
              alt={alt}
              width={1200}
              height={900}
              className="w-full h-auto max-h-[80vh] object-contain"
              priority
            />
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Image caption */}
      {alt && (
        <p className="text-sm text-muted-foreground text-center mt-2 italic">
          {alt}
        </p>
      )}
    </div>
  )
}

export function ChapterContentRenderer({ 
  content, 
  fontSize = 16, 
  className 
}: ChapterContentRendererProps) {
  // Parse content for image markdown syntax: ![alt text](image_url)
  const parseContentWithImages = useCallback((text: string) => {
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g
    const images: ImageMatch[] = []
    let match

    // Find all image matches
    while ((match = imageRegex.exec(text)) !== null) {
      images.push({
        fullMatch: match[0],
        alt: match[1] || "",
        src: match[2],
        index: match.index
      })
    }

    if (images.length === 0) {
      // No images found, return content as-is
      return [{ type: 'text', content: text }]
    }

    const parts = []
    let lastIndex = 0

    // Split content around images
    images.forEach((image, index) => {
      // Add text before image
      if (image.index > lastIndex) {
        const textContent = text.slice(lastIndex, image.index)
        if (textContent.trim()) {
          parts.push({ type: 'text', content: textContent })
        }
      }

      // Add image
      parts.push({
        type: 'image',
        src: image.src,
        alt: image.alt,
        key: `image-${index}`
      })

      lastIndex = image.index + image.fullMatch.length
    })

    // Add remaining text after last image
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex)
      if (remainingText.trim()) {
        parts.push({ type: 'text', content: remainingText })
      }
    }

    return parts
  }, [])

  const contentParts = parseContentWithImages(content)

  return (
    <div 
      className={cn("prose-reading custom-scrollbar", className)}
      style={{ fontSize: `${fontSize}px` }}
    >
      {contentParts.map((part, index) => {
        if (part.type === 'image') {
          return (
            <ChapterImage
              key={part.key || `image-${index}`}
              src={part.src}
              alt={part.alt}
              className="my-6"
            />
          )
        }

        // Render text content with preserved formatting
        return (
          <div
            key={`text-${index}`}
            className="whitespace-pre-wrap leading-relaxed"
            dangerouslySetInnerHTML={{ __html: part.content }}
          />
        )
      })}
    </div>
  )
}
