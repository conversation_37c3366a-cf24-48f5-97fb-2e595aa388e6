"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { HelpCircle } from "lucide-react"

export function ImageHelpTooltip() {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <HelpCircle className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="max-w-sm">
          <div className="space-y-2">
            <p className="font-medium">How to add images:</p>
            <ol className="text-sm space-y-1 list-decimal list-inside">
              <li>Click "Add Image" to upload an image</li>
              <li>Add a description for accessibility</li>
              <li>Click "Insert Image" to add it to your content</li>
            </ol>
            <p className="text-xs text-muted-foreground mt-2">
              You can also manually type: ![description](image_url)
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
