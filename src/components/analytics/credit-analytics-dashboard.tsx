"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { 
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  CreditCard,
  Target,
  RefreshCw,
  Download,
  Calendar,
  Coins,
  BookOpen,
  Activity
} from "lucide-react"
import { formatCredits, formatCurrency } from "@/lib/credits"

interface AnalyticsData {
  period: string
  dateRange: {
    start: string
    end: string
  }
  overview: {
    totalUsers: number
    activeUsers: number
    totalCreditsSold: number
    totalRevenue: number
    totalTransactions: number
    averageTransactionValue: number
    conversionRate: number
  }
  trends: {
    userGrowth: Array<{ period: string; new_users: number }>
    revenueGrowth: Array<{ period: string; revenue: number; transactions: number }>
  }
  packages: {
    topSelling: Array<{
      packageId: string
      _count: { packageId: number }
      _sum: { amount: number; totalCredits: number }
      package: {
        id: string
        name: string
        credits: number
        price: number
      }
    }>
  }
  content: {
    topContent: Array<{
      contentType: string
      contentId: string
      _count: { contentId: number }
      _sum: { creditsSpent: number }
    }>
    revenueByType: Array<{
      contentType: string
      _sum: { creditsSpent: number; priceAtTime: number }
      _count: { contentType: number }
    }>
  }
  userBehavior: {
    balanceDistribution: Array<{ balance_range: string; user_count: number }>
    spendingPatterns: {
      avg_credits_per_purchase: number
      min_credits: number
      max_credits: number
      total_purchases: number
    }
    retentionMetrics: {
      total_users: number
      repeat_users: number
    }
  }
  conversionMetrics: {
    totalVisitors: number
    creditPurchasers: number
    contentPurchasers: number
    conversionRate: number
    contentConversionRate: number
  }
}

export function CreditAnalyticsDashboard() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [period, setPeriod] = useState('30d')

  useEffect(() => {
    if (session?.user?.role === 'ADMIN') {
      loadAnalytics()
    }
  }, [session, period])

  const loadAnalytics = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/analytics/credits?period=${period}`)
      if (!response.ok) throw new Error('Failed to load analytics')
      
      const analyticsData = await response.json()
      setData(analyticsData)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!session?.user || session.user.role !== 'ADMIN') {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Access denied. Admin privileges required.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Credit System Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive insights into credit usage, revenue, and user behavior
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 days</SelectItem>
              <SelectItem value="30d">30 days</SelectItem>
              <SelectItem value="90d">90 days</SelectItem>
              <SelectItem value="1y">1 year</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadAnalytics}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-4 w-24" />
              </CardContent>
            </Card>
          ))
        ) : data ? (
          <>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(data.overview.totalRevenue)}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                    <p className="text-2xl font-bold">{data.overview.activeUsers.toLocaleString()}</p>
                  </div>
                  <Users className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Credits Sold</p>
                    <p className="text-2xl font-bold">{formatCredits(data.overview.totalCreditsSold)}</p>
                  </div>
                  <Coins className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Conversion Rate</p>
                    <p className="text-2xl font-bold">{data.overview.conversionRate.toFixed(1)}%</p>
                  </div>
                  <Target className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </>
        ) : null}
      </div>

      {/* Charts and Analytics */}
      <Tabs defaultValue="revenue" className="w-full">
        <TabsList>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="packages">Packages</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="behavior">Behavior</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Trends</CardTitle>
              <CardDescription>Credit purchase revenue over time</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-80 w-full" />
              ) : data?.trends.revenueGrowth ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data.trends.revenueGrowth}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-80 flex items-center justify-center text-muted-foreground">
                  No data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>New user registrations</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-60 w-full" />
                ) : data?.trends.userGrowth ? (
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={data.trends.userGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="period" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="new_users" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-60 flex items-center justify-center text-muted-foreground">
                    No data available
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Credit Balance Distribution</CardTitle>
                <CardDescription>User credit balance ranges</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-60 w-full" />
                ) : data?.userBehavior.balanceDistribution ? (
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={data.userBehavior.balanceDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ balance_range, percent }) => `${balance_range} (${(percent * 100).toFixed(0)}%)`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="user_count"
                        nameKey="balance_range"
                      >
                        {data.userBehavior.balanceDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-60 flex items-center justify-center text-muted-foreground">
                    No data available
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="packages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Selling Packages</CardTitle>
              <CardDescription>Most popular credit packages</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : data?.packages.topSelling ? (
                <div className="space-y-4">
                  {data.packages.topSelling.map((pkg, index) => (
                    <div key={pkg.packageId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Badge variant="outline">#{index + 1}</Badge>
                        <div>
                          <h4 className="font-semibold">{pkg.package.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {formatCredits(pkg.package.credits)} for ${pkg.package.price}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{pkg._count.packageId} sales</p>
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(pkg._sum.amount)} revenue
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No package data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Revenue by Content Type</CardTitle>
                <CardDescription>Credit spending by content category</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-60 w-full" />
                ) : data?.content.revenueByType ? (
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={data.content.revenueByType}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ contentType, percent }) => `${contentType} (${(percent * 100).toFixed(0)}%)`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="_sum.creditsSpent"
                        nameKey="contentType"
                      >
                        {data.content.revenueByType.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [formatCredits(Number(value)), 'Credits Spent']} />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-60 flex items-center justify-center text-muted-foreground">
                    No content data available
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Content Performance</CardTitle>
                <CardDescription>Most purchased content</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Skeleton key={i} className="h-12 w-full" />
                    ))}
                  </div>
                ) : data?.content.topContent ? (
                  <div className="space-y-3">
                    {data.content.topContent.slice(0, 5).map((content, index) => (
                      <div key={`${content.contentType}-${content.contentId}`} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-3">
                          <Badge variant="outline">#{index + 1}</Badge>
                          <div>
                            <p className="font-medium">{content.contentType}</p>
                            <p className="text-sm text-muted-foreground">ID: {content.contentId}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">{content._count.contentId} purchases</p>
                          <p className="text-sm text-muted-foreground">
                            {formatCredits(content._sum.creditsSpent)} spent
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No content data available
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="behavior" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Spending Patterns</CardTitle>
                <CardDescription>Average credit usage</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-24" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                ) : data?.userBehavior.spendingPatterns ? (
                  <div className="space-y-3">
                    <div>
                      <p className="text-2xl font-bold">
                        {formatCredits(Math.round(data.userBehavior.spendingPatterns.avg_credits_per_purchase))}
                      </p>
                      <p className="text-sm text-muted-foreground">Average per purchase</p>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      <p>Min: {formatCredits(data.userBehavior.spendingPatterns.min_credits)}</p>
                      <p>Max: {formatCredits(data.userBehavior.spendingPatterns.max_credits)}</p>
                    </div>
                  </div>
                ) : null}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Retention</CardTitle>
                <CardDescription>Repeat purchase rate</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                ) : data?.userBehavior.retentionMetrics ? (
                  <div className="space-y-3">
                    <div>
                      <p className="text-2xl font-bold">
                        {((data.userBehavior.retentionMetrics.repeat_users / data.userBehavior.retentionMetrics.total_users) * 100).toFixed(1)}%
                      </p>
                      <p className="text-sm text-muted-foreground">Repeat customers</p>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      <p>{data.userBehavior.retentionMetrics.repeat_users} of {data.userBehavior.retentionMetrics.total_users} users</p>
                    </div>
                  </div>
                ) : null}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Funnel</CardTitle>
                <CardDescription>User journey metrics</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                ) : data?.conversionMetrics ? (
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Visitors</span>
                        <span>{data.conversionMetrics.totalVisitors}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '100%' }}></div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Credit Buyers</span>
                        <span>{data.conversionMetrics.creditPurchasers}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${data.conversionMetrics.conversionRate}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Content Buyers</span>
                        <span>{data.conversionMetrics.contentPurchasers}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ width: `${data.conversionMetrics.contentConversionRate}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ) : null}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
