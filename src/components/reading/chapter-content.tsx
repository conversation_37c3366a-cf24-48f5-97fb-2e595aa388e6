"use client"

import { ContentGuard } from "@/components/paywall/content-guard"
import { PremiumBadge } from "@/components/paywall/premium-badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ChevronLeft, ChevronRight, BookOpen } from "lucide-react"
import Link from "next/link"
import type { Chapter, Novel } from "@prisma/client"
import { ChapterContentRenderer } from "@/components/chapter/chapter-content-renderer"

interface ChapterWithNovel extends Chapter {
  novel: Pick<Novel, "id" | "title" | "authorId" | "isPremium" | "requiredTier"> & {
    author: {
      id: string
      name: string | null
      image: string | null
    }
  }
}

interface ChapterContentProps {
  chapter: ChapterWithNovel
  previousChapter?: { id: string; title: string; order: number }
  nextChapter?: { id: string; title: string; order: number }
}

export function ChapterContent({ chapter, previousChapter, nextChapter }: ChapterContentProps) {
  const isPremium = chapter.isPremium || chapter.novel.isPremium
  const requiredTier = chapter.requiredTier || chapter.novel.requiredTier

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Chapter Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Link 
                  href={`/novels/${chapter.novel.id}`}
                  className="text-sm text-muted-foreground hover:text-primary flex items-center gap-1"
                >
                  <BookOpen className="h-4 w-4" />
                  {chapter.novel.title}
                </Link>
                <PremiumBadge 
                  isPremium={isPremium} 
                  requiredTier={requiredTier}
                  size="sm"
                />
              </div>
              <CardTitle className="text-2xl">
                Chapter {chapter.order}: {chapter.title}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                by{" "}
                <Link 
                  href={`/authors/${chapter.novel.author.id}`}
                  className="hover:text-primary"
                >
                  {chapter.novel.author.name}
                </Link>
              </p>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Chapter Content with Paywall Protection */}
      <Card>
        <CardContent className="pt-6">
          <ContentGuard
            isPremium={isPremium}
            requiredTier={requiredTier}
            contentType="chapter"
            contentTitle={chapter.title}
            authorId={chapter.novel.authorId}
            authorName={chapter.novel.author.name || undefined}
            showPreview={true}
            previewLength={300}
          >
            <div className="prose prose-lg max-w-none">
              {/* Render chapter content */}
              <ChapterContentRenderer
                content={chapter.content}
                fontSize={18}
              />
            </div>
          </ContentGuard>
        </CardContent>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {previousChapter ? (
                <Button asChild variant="outline" className="w-full max-w-xs">
                  <Link href={`/chapters/${previousChapter.id}`} className="flex items-center gap-2">
                    <ChevronLeft className="h-4 w-4" />
                    <div className="text-left">
                      <div className="text-xs text-muted-foreground">Previous</div>
                      <div className="truncate">Chapter {previousChapter.order}: {previousChapter.title}</div>
                    </div>
                  </Link>
                </Button>
              ) : (
                <div className="w-full max-w-xs" />
              )}
            </div>

            <div className="flex-shrink-0 mx-4">
              <Button asChild variant="ghost">
                <Link href={`/novels/${chapter.novel.id}`}>
                  <BookOpen className="h-4 w-4 mr-2" />
                  Table of Contents
                </Link>
              </Button>
            </div>

            <div className="flex-1 flex justify-end">
              {nextChapter ? (
                <Button asChild variant="outline" className="w-full max-w-xs">
                  <Link href={`/chapters/${nextChapter.id}`} className="flex items-center gap-2">
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">Next</div>
                      <div className="truncate">Chapter {nextChapter.order}: {nextChapter.title}</div>
                    </div>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              ) : (
                <div className="w-full max-w-xs" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Author Support Section (for premium content) */}
      {isPremium && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <h3 className="font-semibold text-lg">Support the Author</h3>
              <p className="text-sm text-muted-foreground">
                Enjoying this premium content? Show your appreciation to {chapter.novel.author.name}!
              </p>
              <div className="flex gap-2 justify-center">
                <Button size="sm" variant="outline">
                  Leave a Tip
                </Button>
                <Button size="sm" variant="outline">
                  Follow Author
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
