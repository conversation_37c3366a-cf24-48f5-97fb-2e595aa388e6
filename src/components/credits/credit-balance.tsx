"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useDispatch, useSelector } from "react-redux"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { Coins, Plus, RefreshCw } from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { CreditPurchaseModal } from "./credit-purchase-modal"
import { useGetCreditBalanceQuery } from "@/store/api/creditsApi"
import {
  selectCreditBalance,
  selectCreditLoading,
  selectShouldShowLowBalanceWarning,
  setCreditBalance,
  openPurchaseModal,
  checkAndShowLowBalanceWarning
} from "@/store/slices/creditSlice"
import type { AppDispatch } from "@/lib/store"

interface CreditBalanceProps {
  showPurchaseButton?: boolean
  className?: string
}

export function CreditBalance({
  showPurchaseButton = true,
  className
}: CreditBalanceProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const dispatch = useDispatch<AppDispatch>()

  // Redux state
  const balance = useSelector(selectCreditBalance)
  const isLoading = useSelector(selectCreditLoading)
  const shouldShowLowBalanceWarning = useSelector(selectShouldShowLowBalanceWarning)

  // RTK Query
  const {
    data: balanceData,
    isLoading: isQueryLoading,
    refetch
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 30000, // Poll every 30 seconds
  })

  const [isRefreshing, setIsRefreshing] = useState(false)

  // Update Redux state when balance data changes
  useEffect(() => {
    if (balanceData?.balance !== undefined) {
      dispatch(setCreditBalance(balanceData.balance))
    }
  }, [balanceData, dispatch])

  // Check for low balance warning
  useEffect(() => {
    if (balance > 0) {
      dispatch(checkAndShowLowBalanceWarning())
    }
  }, [balance, dispatch])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await refetch()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh credit balance",
        variant: "destructive"
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  const handlePurchaseClick = () => {
    dispatch(openPurchaseModal(null))
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center text-muted-foreground">
            <Coins className="h-5 w-5 mr-2" />
            Sign in to view credits
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Coins className="h-5 w-5 text-yellow-500" />
            Credit Balance
          </CardTitle>
          <CardDescription>
            Use credits to unlock premium content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isLoading || isQueryLoading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <Badge variant="secondary" className="text-lg font-bold px-3 py-1">
                  {formatCredits(balance)}
                </Badge>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>

            {showPurchaseButton && (
              <Button
                onClick={handlePurchaseClick}
                size="sm"
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Buy Credits
              </Button>
            )}
          </div>

          {shouldShowLowBalanceWarning && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                Your credit balance is low. Consider purchasing more credits to continue enjoying premium content.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <CreditPurchaseModal />
    </>
  )
}
