"use client"

import { useEffect } from "react"
import { useSession } from "next-auth/react"
import { useDispatch, useSelector } from "react-redux"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Coins, 
  Plus, 
  RefreshCw,
  AlertTriangle,
  TrendingUp,
  Zap
} from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { MobileCreditPurchase } from "./mobile-credit-purchase"
import { useGetCreditBalanceQuery } from "@/store/api/creditsApi"
import { 
  selectCreditBalance, 
  selectCreditLoading, 
  selectShouldShowLowBalanceWarning,
  setCreditBalance,
  openPurchaseModal,
  checkAndShowLowBalanceWarning,
  dismissLowBalanceWarning
} from "@/store/slices/creditSlice"
import type { AppDispatch } from "@/lib/store"

interface MobileCreditBalanceProps {
  compact?: boolean
  showPurchaseButton?: boolean
  className?: string
}

export function MobileCreditBalance({ 
  compact = false,
  showPurchaseButton = true,
  className 
}: MobileCreditBalanceProps) {
  const { data: session } = useSession()
  const dispatch = useDispatch<AppDispatch>()
  
  // Redux state
  const balance = useSelector(selectCreditBalance)
  const isLoading = useSelector(selectCreditLoading)
  const shouldShowLowBalanceWarning = useSelector(selectShouldShowLowBalanceWarning)
  
  // RTK Query
  const { 
    data: balanceData, 
    isLoading: isQueryLoading, 
    refetch 
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 60000, // Poll every minute on mobile
  })

  // Update Redux state when balance data changes
  useEffect(() => {
    if (balanceData?.balance !== undefined) {
      dispatch(setCreditBalance(balanceData.balance))
    }
  }, [balanceData, dispatch])

  // Check for low balance warning
  useEffect(() => {
    if (balance > 0) {
      dispatch(checkAndShowLowBalanceWarning())
    }
  }, [balance, dispatch])

  const handleRefresh = async () => {
    try {
      await refetch()
    } catch (error) {
      console.error('Failed to refresh balance:', error)
    }
  }

  const handleDismissWarning = () => {
    dispatch(dismissLowBalanceWarning())
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center justify-center text-muted-foreground">
            <Coins className="h-5 w-5 mr-2" />
            <span className="text-sm">Sign in to view credits</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="flex items-center gap-1">
          <Coins className="h-4 w-4 text-yellow-500" />
          {isLoading || isQueryLoading ? (
            <Skeleton className="h-5 w-12" />
          ) : (
            <Badge variant="secondary" className="text-sm font-semibold">
              {formatCredits(balance)}
            </Badge>
          )}
        </div>
        
        {showPurchaseButton && (
          <MobileCreditPurchase 
            trigger={
              <Button size="sm" variant="outline" className="h-7 px-2">
                <Plus className="h-3 w-3" />
              </Button>
            }
            onSuccess={handleRefresh}
          />
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Coins className="h-5 w-5 text-yellow-500" />
              <span className="font-medium">Credits</span>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading || isQueryLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-4 w-4 ${(isLoading || isQueryLoading) ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              {isLoading || isQueryLoading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold">
                  {balance.toLocaleString()}
                </div>
              )}
              <div className="text-sm text-muted-foreground">
                Available credits
              </div>
            </div>

            {showPurchaseButton && (
              <MobileCreditPurchase 
                trigger={
                  <Button size="sm" className="gap-2">
                    <Plus className="h-4 w-4" />
                    Buy More
                  </Button>
                }
                onSuccess={handleRefresh}
              />
            )}
          </div>

          {/* Quick stats */}
          <div className="mt-4 pt-3 border-t">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-green-600">
                  ${(balance * 0.10).toFixed(2)}
                </div>
                <div className="text-xs text-muted-foreground">
                  Credit Value
                </div>
              </div>
              <div>
                <div className="text-lg font-semibold text-blue-600">
                  {Math.floor(balance / 5)}
                </div>
                <div className="text-xs text-muted-foreground">
                  Chapters Available
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Low balance warning */}
      {shouldShowLowBalanceWarning && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="font-medium text-sm">Low Credit Balance</p>
                <p className="text-xs">Consider purchasing more credits to continue reading.</p>
              </div>
              <div className="flex items-center gap-2 ml-2">
                <MobileCreditPurchase 
                  trigger={
                    <Button size="sm" variant="outline" className="h-7 text-xs">
                      <Zap className="h-3 w-3 mr-1" />
                      Top Up
                    </Button>
                  }
                  onSuccess={() => {
                    handleRefresh()
                    handleDismissWarning()
                  }}
                />
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={handleDismissWarning}
                  className="h-7 w-7 p-0 text-yellow-600"
                >
                  ×
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Usage tips for mobile */}
      {balance > 0 && balance < 50 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              <TrendingUp className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Pro Tip</p>
                <p className="text-xs">
                  Buy credits in bulk to get bonus credits and better value!
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
