"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import { useToast } from "@/hooks/use-toast"
import { 
  Lock, 
  Coins, 
  Crown, 
  CreditCard, 
  ShoppingCart,
  Zap,
  AlertTriangle,
  ArrowRight,
  Star,
  Unlock
} from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { SUBSCRIPTION_TIERS } from "@/lib/stripe"
import { MobileCreditPurchase } from "./mobile-credit-purchase"

interface MobileContentPaywallProps {
  contentType: 'NOVEL' | 'CHAPTER'
  contentId: string
  contentTitle: string
  accessInfo: {
    hasAccess: boolean
    reason?: string
    requiredTier?: string
    currentTier?: string
    creditPrice?: number
    canPurchaseWithCredits?: boolean
    userCreditBalance?: number
    hasPurchased?: boolean
    purchaseOptions?: {
      subscription: {
        available: boolean
        requiredTier?: string
      }
      credits: {
        available: boolean
        price?: number
        canAfford: boolean
        userBalance: number
      }
    }
  }
  onAccessGranted?: () => void
  className?: string
}

export function MobileContentPaywall({ 
  contentType, 
  contentId, 
  contentTitle,
  accessInfo,
  onAccessGranted,
  className 
}: MobileContentPaywallProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [showOptions, setShowOptions] = useState(false)
  const [isPurchasing, setIsPurchasing] = useState(false)

  // If user has access, don't show paywall
  if (accessInfo.hasAccess) {
    return null
  }

  const handleCreditPurchase = async () => {
    if (!session) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to purchase content",
        variant: "destructive"
      })
      return
    }

    if (!accessInfo.creditPrice) {
      toast({
        title: "Not Available",
        description: "This content is not available for credit purchase",
        variant: "destructive"
      })
      return
    }

    setIsPurchasing(true)
    try {
      const response = await fetch('/api/credits/spend', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          contentId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Purchase failed')
      }

      const result = await response.json()
      
      toast({
        title: "Content Unlocked! 🎉",
        description: `Successfully unlocked "${contentTitle}"`,
      })

      onAccessGranted?.()
    } catch (error: any) {
      console.error('Credit purchase error:', error)
      
      if (error.message.includes('Insufficient credits')) {
        toast({
          title: "Insufficient Credits",
          description: "You don't have enough credits. Purchase more to unlock this content.",
          variant: "destructive",
        })
      } else {
        toast({
          title: "Purchase Failed",
          description: error.message || "Failed to unlock content. Please try again.",
          variant: "destructive",
        })
      }
    } finally {
      setIsPurchasing(false)
    }
  }

  const handleSubscriptionUpgrade = () => {
    router.push('/pricing')
  }

  const getRequiredTierInfo = () => {
    if (!accessInfo.requiredTier) return null
    
    const tierKey = accessInfo.requiredTier as keyof typeof SUBSCRIPTION_TIERS
    return SUBSCRIPTION_TIERS[tierKey]
  }

  const requiredTierInfo = getRequiredTierInfo()
  const canAffordWithCredits = accessInfo.purchaseOptions?.credits.canAfford
  const hasCreditsOption = accessInfo.purchaseOptions?.credits.available
  const hasSubscriptionOption = accessInfo.purchaseOptions?.subscription.available

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main paywall card */}
      <Card className="border-2 border-dashed border-gray-300 bg-gradient-to-br from-gray-50 to-gray-100">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto mb-3 p-3 bg-gray-200 rounded-full w-fit">
            <Lock className="h-6 w-6 text-gray-600" />
          </div>
          <CardTitle className="flex items-center justify-center gap-2 text-lg">
            <Crown className="h-5 w-5 text-yellow-500" />
            Premium Content
          </CardTitle>
          <CardDescription className="text-sm">
            Unlock "{contentTitle}" to continue reading
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Quick unlock button */}
          {hasCreditsOption && canAffordWithCredits && (
            <Button 
              onClick={handleCreditPurchase}
              disabled={isPurchasing}
              className="w-full h-12 text-base bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isPurchasing ? (
                <>
                  <Zap className="h-5 w-5 mr-2 animate-pulse" />
                  Unlocking...
                </>
              ) : (
                <>
                  <Unlock className="h-5 w-5 mr-2" />
                  Unlock for {formatCredits(accessInfo.creditPrice!)}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          )}

          {/* Insufficient credits warning */}
          {hasCreditsOption && !canAffordWithCredits && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="font-medium text-sm">Need More Credits</p>
                    <p className="text-xs">
                      You need {formatCredits(accessInfo.creditPrice! - accessInfo.purchaseOptions!.credits.userBalance)} more credits
                    </p>
                  </div>
                  <MobileCreditPurchase 
                    trigger={
                      <Button size="sm" className="ml-2">
                        <Coins className="h-4 w-4 mr-1" />
                        Buy
                      </Button>
                    }
                  />
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* More options button */}
          <Sheet open={showOptions} onOpenChange={setShowOptions}>
            <SheetTrigger asChild>
              <Button variant="outline" className="w-full">
                View All Options
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </SheetTrigger>
            
            <SheetContent side="bottom" className="h-[80vh]">
              <SheetHeader className="mb-6">
                <SheetTitle>Unlock Options</SheetTitle>
                <SheetDescription>
                  Choose how you'd like to access "{contentTitle}"
                </SheetDescription>
              </SheetHeader>

              <div className="space-y-4">
                {/* Credit option */}
                {hasCreditsOption && (
                  <Card className={canAffordWithCredits ? "border-green-200 bg-green-50" : "border-gray-200"}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Coins className="h-5 w-5 text-yellow-500" />
                          <span className="font-medium">Pay with Credits</span>
                        </div>
                        <Badge variant="secondary" className="text-lg">
                          {formatCredits(accessInfo.creditPrice!)}
                        </Badge>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span>Your balance:</span>
                          <span className="font-medium">
                            {formatCredits(accessInfo.purchaseOptions!.credits.userBalance)}
                          </span>
                        </div>
                        
                        {canAffordWithCredits ? (
                          <Button 
                            onClick={() => {
                              setShowOptions(false)
                              handleCreditPurchase()
                            }}
                            disabled={isPurchasing}
                            className="w-full"
                          >
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Unlock Now
                          </Button>
                        ) : (
                          <div className="space-y-2">
                            <Alert>
                              <AlertTriangle className="h-4 w-4" />
                              <AlertDescription className="text-sm">
                                You need {formatCredits(accessInfo.creditPrice! - accessInfo.purchaseOptions!.credits.userBalance)} more credits
                              </AlertDescription>
                            </Alert>
                            <MobileCreditPurchase 
                              trigger={
                                <Button variant="outline" className="w-full">
                                  <Coins className="h-4 w-4 mr-2" />
                                  Buy More Credits
                                </Button>
                              }
                            />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Subscription option */}
                {hasSubscriptionOption && requiredTierInfo && (
                  <Card className="border-purple-200 bg-purple-50">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Crown className="h-5 w-5 text-purple-500" />
                          <span className="font-medium">Subscribe</span>
                        </div>
                        <Badge variant="outline" className="border-purple-300">
                          ${requiredTierInfo.price}/month
                        </Badge>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="text-sm text-muted-foreground">
                          <p className="font-medium mb-1">Get unlimited access to:</p>
                          <ul className="text-xs space-y-1">
                            {requiredTierInfo.features.slice(0, 3).map((feature, index) => (
                              <li key={index} className="flex items-center gap-1">
                                <Star className="h-3 w-3 text-purple-500" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <Button 
                          onClick={() => {
                            setShowOptions(false)
                            handleSubscriptionUpgrade()
                          }}
                          className="w-full bg-purple-600 hover:bg-purple-700"
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Upgrade to {requiredTierInfo.name}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Help text */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 text-center">
                  Credits are perfect for occasional reading, while subscriptions give you unlimited access to all premium content.
                </p>
              </div>
            </SheetContent>
          </Sheet>
        </CardContent>
      </Card>
    </div>
  )
}
