"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { 
  Lock, 
  Coins, 
  Crown, 
  CreditCard, 
  ShoppingCart,
  Zap,
  AlertTriangle
} from "lucide-react"
import { formatCredits, formatCreditPrice } from "@/lib/credits"
import { SUBSCRIPTION_TIERS } from "@/lib/stripe"
import { CreditPurchaseModal } from "./credit-purchase-modal"

interface ContentPaywallProps {
  contentType: 'NOVEL' | 'CHAPTER'
  contentId: string
  contentTitle: string
  accessInfo: {
    hasAccess: boolean
    reason?: string
    requiredTier?: string
    currentTier?: string
    creditPrice?: number
    canPurchaseWithCredits?: boolean
    userCreditBalance?: number
    hasPurchased?: boolean
    purchaseOptions?: {
      subscription: {
        available: boolean
        requiredTier?: string
      }
      credits: {
        available: boolean
        price?: number
        canAfford: boolean
        userBalance: number
      }
    }
  }
  onAccessGranted?: () => void
  className?: string
}

export function ContentPaywall({ 
  contentType, 
  contentId, 
  contentTitle,
  accessInfo,
  onAccessGranted,
  className 
}: ContentPaywallProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [showCreditPurchase, setShowCreditPurchase] = useState(false)
  const [isPurchasing, setIsPurchasing] = useState(false)

  // If user has access, don't show paywall
  if (accessInfo.hasAccess) {
    return null
  }

  const handleCreditPurchase = async () => {
    if (!session) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to purchase content",
        variant: "destructive"
      })
      return
    }

    if (!accessInfo.creditPrice) {
      toast({
        title: "Not Available",
        description: "This content is not available for credit purchase",
        variant: "destructive"
      })
      return
    }

    setIsPurchasing(true)
    try {
      const response = await fetch('/api/credits/spend', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          contentId,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Purchase failed')
      }

      const result = await response.json()
      
      toast({
        title: "Content Unlocked!",
        description: `Successfully unlocked "${contentTitle}" for ${formatCredits(result.creditsSpent)}`,
      })

      onAccessGranted?.()
    } catch (error: any) {
      console.error('Credit purchase error:', error)
      
      if (error.message.includes('Insufficient credits')) {
        toast({
          title: "Insufficient Credits",
          description: "You don't have enough credits. Purchase more credits to unlock this content.",
          variant: "destructive",
        })
        setShowCreditPurchase(true)
      } else {
        toast({
          title: "Purchase Failed",
          description: error.message || "Failed to unlock content. Please try again.",
          variant: "destructive",
        })
      }
    } finally {
      setIsPurchasing(false)
    }
  }

  const handleSubscriptionUpgrade = () => {
    router.push('/pricing')
  }

  const getRequiredTierInfo = () => {
    if (!accessInfo.requiredTier) return null
    
    const tierKey = accessInfo.requiredTier as keyof typeof SUBSCRIPTION_TIERS
    return SUBSCRIPTION_TIERS[tierKey]
  }

  const requiredTierInfo = getRequiredTierInfo()

  return (
    <>
      <Card className={`border-2 border-dashed border-gray-300 ${className}`}>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-gray-100 rounded-full w-fit">
            <Lock className="h-8 w-8 text-gray-600" />
          </div>
          <CardTitle className="flex items-center justify-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Premium Content
          </CardTitle>
          <CardDescription>
            This {contentType.toLowerCase()} requires a subscription or credit purchase to access
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Content Info */}
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-lg mb-2">{contentTitle}</h3>
            {requiredTierInfo && (
              <Badge variant="outline" className="mb-2">
                Requires {requiredTierInfo.name}
              </Badge>
            )}
          </div>

          {/* Access Options */}
          <div className="space-y-3">
            {/* Credit Purchase Option */}
            {accessInfo.purchaseOptions?.credits.available && (
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Coins className="h-5 w-5 text-yellow-500" />
                    <span className="font-medium">Pay with Credits</span>
                  </div>
                  <Badge variant="secondary">
                    {formatCredits(accessInfo.creditPrice!)}
                  </Badge>
                </div>
                
                {session ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Your balance:</span>
                      <span className="font-medium">
                        {formatCredits(accessInfo.purchaseOptions.credits.userBalance)}
                      </span>
                    </div>
                    
                    {accessInfo.purchaseOptions.credits.canAfford ? (
                      <Button 
                        onClick={handleCreditPurchase}
                        disabled={isPurchasing}
                        className="w-full"
                      >
                        {isPurchasing ? (
                          <>
                            <Zap className="h-4 w-4 mr-2 animate-pulse" />
                            Unlocking...
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Unlock for {formatCredits(accessInfo.creditPrice!)}
                          </>
                        )}
                      </Button>
                    ) : (
                      <div className="space-y-2">
                        <Alert>
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            You need {formatCredits(accessInfo.creditPrice! - accessInfo.purchaseOptions.credits.userBalance)} more credits
                          </AlertDescription>
                        </Alert>
                        <Button 
                          onClick={() => setShowCreditPurchase(true)}
                          variant="outline"
                          className="w-full"
                        >
                          <Coins className="h-4 w-4 mr-2" />
                          Buy More Credits
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <Button 
                    onClick={() => router.push('/auth/signin')}
                    variant="outline"
                    className="w-full"
                  >
                    Sign In to Purchase
                  </Button>
                )}
              </div>
            )}

            {/* Subscription Option */}
            {accessInfo.purchaseOptions?.subscription.available && requiredTierInfo && (
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Crown className="h-5 w-5 text-purple-500" />
                    <span className="font-medium">Subscribe</span>
                  </div>
                  <Badge variant="outline">
                    ${requiredTierInfo.price}/month
                  </Badge>
                </div>
                
                <div className="space-y-2 text-sm text-muted-foreground mb-3">
                  <p>Get unlimited access to all {requiredTierInfo.name} content</p>
                  <ul className="list-disc list-inside space-y-1">
                    {requiredTierInfo.features.slice(0, 3).map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
                
                <Button 
                  onClick={handleSubscriptionUpgrade}
                  className="w-full"
                  variant="outline"
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Upgrade to {requiredTierInfo.name}
                </Button>
              </div>
            )}
          </div>

          {/* Help Text */}
          <div className="text-center text-sm text-muted-foreground">
            <p>
              Credits are a one-time purchase for individual content, while subscriptions give you unlimited access.
            </p>
          </div>
        </CardContent>
      </Card>

      <CreditPurchaseModal
        open={showCreditPurchase}
        onOpenChange={setShowCreditPurchase}
        onSuccess={() => {
          setShowCreditPurchase(false)
          // Refresh the page to update credit balance and access info
          window.location.reload()
        }}
      />
    </>
  )
}
