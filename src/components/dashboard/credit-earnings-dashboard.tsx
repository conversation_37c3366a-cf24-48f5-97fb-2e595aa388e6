"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { 
  Coins, 
  TrendingUp, 
  DollarSign, 
  Users,
  Calendar,
  RefreshCw,
  CreditCard,
  Zap
} from "lucide-react"
import { formatCurrency } from "@/lib/stripe"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { format } from "date-fns"

interface EarningSummary {
  summary: {
    totalEarnings: number
    totalRevenue: number
    unpaidAmount: number
    totalTransactions: number
    period: {
      days: number
      earnings: number
      revenue: number
      transactions: number
    }
  }
  creditMetrics: {
    totalCreditTransactions: number
    totalCreditRevenue: number
    totalCreditEarnings: number
    averageCreditTransaction: number
    creditEarningsThisPeriod: number
  }
  byType: {
    allTime: Record<string, any>
    period: Record<string, any>
  }
}

export function CreditEarningsDashboard() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [summary, setSummary] = useState<EarningSummary | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [period, setPeriod] = useState('30')

  useEffect(() => {
    if (session?.user?.role === 'AUTHOR') {
      loadEarningsSummary()
    }
  }, [session, period])

  const loadEarningsSummary = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/earnings/summary?period=${period}`)
      if (!response.ok) throw new Error('Failed to load earnings summary')
      
      const data = await response.json()
      setSummary(data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load earnings summary",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!session?.user || session.user.role !== 'AUTHOR') {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            This dashboard is only available for authors.
          </p>
        </CardContent>
      </Card>
    )
  }

  const creditStats = summary?.creditMetrics
  const periodStats = summary?.summary.period

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Credit Earnings</h2>
          <p className="text-muted-foreground">
            Track your earnings from credit-based content purchases
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">7 days</SelectItem>
              <SelectItem value="30">30 days</SelectItem>
              <SelectItem value="90">90 days</SelectItem>
              <SelectItem value="365">1 year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadEarningsSummary}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Credit Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Total Credit Revenue
                    </p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(creditStats?.totalCreditRevenue || 0)}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      All time earnings from credits
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-green-100">
                    <DollarSign className="h-5 w-5 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Credit Transactions
                    </p>
                    <p className="text-2xl font-bold">
                      {creditStats?.totalCreditTransactions || 0}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Total content purchases
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-blue-100">
                    <CreditCard className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Average Transaction
                    </p>
                    <p className="text-2xl font-bold">
                      {formatCurrency(creditStats?.averageCreditTransaction || 0)}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Per credit purchase
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-purple-100">
                    <TrendingUp className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Period Earnings
                    </p>
                    <p className="text-2xl font-bold text-orange-600">
                      {formatCurrency(creditStats?.creditEarningsThisPeriod || 0)}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Last {period} days
                    </p>
                  </div>
                  <div className="p-2 rounded-full bg-orange-100">
                    <Calendar className="h-5 w-5 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Earnings Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Credit vs Subscription Comparison */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Coins className="h-5 w-5 text-yellow-500" />
              Revenue Breakdown
            </CardTitle>
            <CardDescription>
              Compare earnings from different sources
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {summary?.byType.allTime && Object.entries(summary.byType.allTime).map(([type, data]) => (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {type === 'CREDIT_PURCHASE' && <Coins className="h-4 w-4 text-yellow-500" />}
                      {type === 'SUBSCRIPTION_REVENUE' && <CreditCard className="h-4 w-4 text-blue-500" />}
                      {type === 'TIP' && <Zap className="h-4 w-4 text-purple-500" />}
                      <span className="font-medium">
                        {type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{formatCurrency(data.authorEarning)}</p>
                      <p className="text-xs text-muted-foreground">{data.count} transactions</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recent Performance
            </CardTitle>
            <CardDescription>
              Last {period} days vs all time
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Earnings</p>
                  <p className="text-xl font-bold">{formatCurrency(periodStats?.earnings || 0)}</p>
                  <p className="text-xs text-muted-foreground">
                    {periodStats?.transactions || 0} transactions
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Credit Earnings</p>
                  <p className="text-xl font-bold text-yellow-600">
                    {formatCurrency(creditStats?.creditEarningsThisPeriod || 0)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {Math.round(((creditStats?.creditEarningsThisPeriod || 0) / (periodStats?.earnings || 1)) * 100)}% of total
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">Unpaid Balance</p>
                  <p className="text-xl font-bold text-blue-600">
                    {formatCurrency(summary?.summary.unpaidAmount || 0)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Ready for payout
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Revenue Share Info */}
      <Card>
        <CardHeader>
          <CardTitle>Revenue Share Information</CardTitle>
          <CardDescription>
            How credit purchases are split between you and the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">70%</p>
              <p className="text-sm font-medium">Your Share</p>
              <p className="text-xs text-muted-foreground">From each credit purchase</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-600">30%</p>
              <p className="text-sm font-medium">Platform Fee</p>
              <p className="text-xs text-muted-foreground">Covers hosting & processing</p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">$50</p>
              <p className="text-sm font-medium">Minimum Payout</p>
              <p className="text-xs text-muted-foreground">Monthly payout threshold</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
