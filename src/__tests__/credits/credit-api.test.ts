/**
 * @jest-environment node
 */

import { createMocks } from 'node-mocks-http'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/db'
import { GET as getCreditBalance } from '@/app/api/credits/balance/route'
import { GET as getCreditTransactions, POST as purchaseCredits } from '@/app/api/credits/purchase/route'
import { POST as spendCredits } from '@/app/api/credits/spend/route'

// Mock dependencies
jest.mock('next-auth')
jest.mock('@/lib/db', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    creditPackage: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    creditPurchase: {
      create: jest.fn(),
    },
    creditTransaction: {
      findMany: jest.fn(),
      create: jest.fn(),
      count: jest.fn(),
    },
    contentPurchase: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    novel: {
      findUnique: jest.fn(),
    },
    chapter: {
      findUnique: jest.fn(),
    },
    earning: {
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}))

jest.mock('@/lib/stripe', () => ({
  stripe: {
    customers: {
      create: jest.fn(),
      update: jest.fn(),
    },
    paymentIntents: {
      create: jest.fn(),
    },
  },
  calculateRevenueSplit: jest.fn(() => ({
    platformFee: 0.30,
    authorEarning: 0.70,
  })),
}))

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('Credit API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/credits/balance', () => {
    it('should return user credit balance', async () => {
      // Mock session
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>' },
      } as any)

      // Mock user with credit balance
      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user1',
        creditBalance: 100,
      } as any)

      const { req, res } = createMocks({ method: 'GET' })
      await getCreditBalance()

      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user1' },
        select: {
          id: true,
          creditBalance: true,
        },
      })
    })

    it('should return 401 for unauthenticated user', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const { req, res } = createMocks({ method: 'GET' })
      const response = await getCreditBalance()

      expect(response.status).toBe(401)
    })

    it('should return 404 for non-existent user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>' },
      } as any)

      mockPrisma.user.findUnique.mockResolvedValue(null)

      const { req, res } = createMocks({ method: 'GET' })
      const response = await getCreditBalance()

      expect(response.status).toBe(404)
    })
  })

  describe('POST /api/credits/spend', () => {
    it('should successfully spend credits on content', async () => {
      // Mock session
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>' },
      } as any)

      // Mock user with sufficient credits
      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user1',
        creditBalance: 50,
      } as any)

      // Mock no existing purchase
      mockPrisma.contentPurchase.findUnique.mockResolvedValue(null)

      // Mock chapter with credit price
      mockPrisma.chapter.findUnique.mockResolvedValue({
        id: 'chapter1',
        title: 'Test Chapter',
        creditPrice: 5,
        novel: {
          author: { id: 'author1' },
        },
      } as any)

      // Mock transaction
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        return await callback(mockPrisma)
      })

      const { req, res } = createMocks({
        method: 'POST',
        body: {
          contentType: 'CHAPTER',
          contentId: 'chapter1',
        },
      })

      await spendCredits(req as any)

      expect(mockPrisma.$transaction).toHaveBeenCalled()
    })

    it('should return error for insufficient credits', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>' },
      } as any)

      // Mock user with insufficient credits
      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user1',
        creditBalance: 2,
      } as any)

      mockPrisma.contentPurchase.findUnique.mockResolvedValue(null)

      mockPrisma.chapter.findUnique.mockResolvedValue({
        id: 'chapter1',
        title: 'Test Chapter',
        creditPrice: 5,
        novel: {
          author: { id: 'author1' },
        },
      } as any)

      const { req, res } = createMocks({
        method: 'POST',
        body: {
          contentType: 'CHAPTER',
          contentId: 'chapter1',
        },
      })

      const response = await spendCredits(req as any)

      expect(response.status).toBe(400)
    })

    it('should return error for already owned content', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>' },
      } as any)

      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user1',
        creditBalance: 50,
      } as any)

      // Mock existing purchase
      mockPrisma.contentPurchase.findUnique.mockResolvedValue({
        id: 'purchase1',
        accessGranted: true,
      } as any)

      const { req, res } = createMocks({
        method: 'POST',
        body: {
          contentType: 'CHAPTER',
          contentId: 'chapter1',
        },
      })

      const response = await spendCredits(req as any)

      expect(response.status).toBe(400)
    })
  })

  describe('Credit Package Management', () => {
    it('should validate credit package data', () => {
      const validPackage = {
        name: 'Test Package',
        credits: 100,
        price: 9.99,
        bonusCredits: 10,
      }

      expect(validPackage.credits).toBeGreaterThan(0)
      expect(validPackage.price).toBeGreaterThan(0)
      expect(validPackage.bonusCredits).toBeGreaterThanOrEqual(0)
    })

    it('should calculate total credits correctly', () => {
      const packageData = {
        credits: 100,
        bonusCredits: 20,
      }

      const totalCredits = packageData.credits + packageData.bonusCredits
      expect(totalCredits).toBe(120)
    })
  })

  describe('Revenue Sharing', () => {
    it('should calculate correct revenue split', () => {
      const creditPrice = 10 // 10 credits = $1.00
      const dollarValue = creditPrice * 0.10
      const platformFee = dollarValue * 0.30
      const authorEarning = dollarValue * 0.70

      expect(dollarValue).toBe(1.00)
      expect(platformFee).toBe(0.30)
      expect(authorEarning).toBe(0.70)
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>' },
      } as any)

      mockPrisma.user.findUnique.mockRejectedValue(new Error('Database error'))

      const { req, res } = createMocks({ method: 'GET' })
      const response = await getCreditBalance()

      expect(response.status).toBe(500)
    })

    it('should validate request data', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user1', email: '<EMAIL>' },
      } as any)

      const { req, res } = createMocks({
        method: 'POST',
        body: {
          contentType: 'INVALID_TYPE',
          contentId: 'chapter1',
        },
      })

      const response = await spendCredits(req as any)

      expect(response.status).toBe(400)
    })
  })
})
