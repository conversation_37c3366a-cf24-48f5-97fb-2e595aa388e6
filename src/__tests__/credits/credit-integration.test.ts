/**
 * Integration tests for the credit system
 * Tests the complete flow from purchase to content access
 */

import { PrismaClient } from '@prisma/client'
import { createMocks } from 'node-mocks-http'
import { getServerSession } from 'next-auth'

// Test database setup
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || 'file:./test.db',
    },
  },
})

// Mock session
jest.mock('next-auth')
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>

describe('Credit System Integration Tests', () => {
  let testUser: any
  let testAuthor: any
  let testNovel: any
  let testChapter: any
  let testCreditPackage: any

  beforeAll(async () => {
    // Set up test data
    testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'READER',
        creditBalance: 0,
      },
    })

    testAuthor = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test Author',
        role: 'AUTHOR',
        creditBalance: 0,
      },
    })

    testNovel = await prisma.novel.create({
      data: {
        title: 'Test Novel',
        description: 'A test novel',
        authorId: testAuthor.id,
        isPremium: true,
        creditPrice: 50,
        status: 'PUBLISHED',
      },
    })

    testChapter = await prisma.chapter.create({
      data: {
        title: 'Test Chapter',
        content: 'Test chapter content',
        novelId: testNovel.id,
        chapterNumber: 1,
        isPremium: true,
        creditPrice: 5,
        status: 'PUBLISHED',
      },
    })

    testCreditPackage = await prisma.creditPackage.create({
      data: {
        name: 'Test Package',
        description: 'Test credit package',
        credits: 100,
        bonusCredits: 10,
        price: 9.99,
        currency: 'usd',
        isActive: true,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    await prisma.creditTransaction.deleteMany()
    await prisma.contentPurchase.deleteMany()
    await prisma.creditPurchase.deleteMany()
    await prisma.earning.deleteMany()
    await prisma.chapter.deleteMany()
    await prisma.novel.deleteMany()
    await prisma.creditPackage.deleteMany()
    await prisma.user.deleteMany()
    await prisma.$disconnect()
  })

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Complete Credit Purchase Flow', () => {
    it('should complete full credit purchase and content unlock flow', async () => {
      // Step 1: User purchases credits
      mockGetServerSession.mockResolvedValue({
        user: { id: testUser.id, email: testUser.email },
      } as any)

      // Simulate successful credit purchase
      const creditPurchase = await prisma.creditPurchase.create({
        data: {
          userId: testUser.id,
          packageId: testCreditPackage.id,
          credits: testCreditPackage.credits,
          bonusCredits: testCreditPackage.bonusCredits,
          totalCredits: testCreditPackage.credits + testCreditPackage.bonusCredits,
          amount: testCreditPackage.price,
          currency: testCreditPackage.currency,
          status: 'COMPLETED',
        },
      })

      // Update user balance
      await prisma.user.update({
        where: { id: testUser.id },
        data: { creditBalance: creditPurchase.totalCredits },
      })

      // Create credit transaction
      await prisma.creditTransaction.create({
        data: {
          userId: testUser.id,
          type: 'PURCHASE',
          status: 'COMPLETED',
          amount: creditPurchase.totalCredits,
          description: `Purchased ${creditPurchase.credits} credits + ${creditPurchase.bonusCredits} bonus`,
          balanceBefore: 0,
          balanceAfter: creditPurchase.totalCredits,
          purchaseId: creditPurchase.id,
        },
      })

      // Verify user has credits
      const updatedUser = await prisma.user.findUnique({
        where: { id: testUser.id },
      })
      expect(updatedUser?.creditBalance).toBe(110) // 100 + 10 bonus

      // Step 2: User spends credits on content
      const contentPurchase = await prisma.contentPurchase.create({
        data: {
          userId: testUser.id,
          contentType: 'CHAPTER',
          contentId: testChapter.id,
          creditsSpent: testChapter.creditPrice!,
          priceAtTime: testChapter.creditPrice! * 0.10, // $0.50
          accessGranted: true,
        },
      })

      // Update user balance after spending
      const newBalance = updatedUser!.creditBalance - testChapter.creditPrice!
      await prisma.user.update({
        where: { id: testUser.id },
        data: { creditBalance: newBalance },
      })

      // Create spending transaction
      await prisma.creditTransaction.create({
        data: {
          userId: testUser.id,
          type: 'SPEND',
          status: 'COMPLETED',
          amount: -testChapter.creditPrice!,
          description: `Unlocked chapter: ${testChapter.title}`,
          balanceBefore: updatedUser!.creditBalance,
          balanceAfter: newBalance,
          contentPurchaseId: contentPurchase.id,
        },
      })

      // Step 3: Create author earning
      const dollarValue = testChapter.creditPrice! * 0.10
      const authorEarning = dollarValue * 0.70
      const platformFee = dollarValue * 0.30

      await prisma.earning.create({
        data: {
          userId: testAuthor.id,
          type: 'CREDIT_PURCHASE',
          amount: dollarValue,
          authorEarning,
          platformFee,
          description: `Credit purchase: ${testChapter.title}`,
          sourceType: 'CHAPTER',
          sourceId: testChapter.id,
          contentPurchaseId: contentPurchase.id,
        },
      })

      // Verify final state
      const finalUser = await prisma.user.findUnique({
        where: { id: testUser.id },
      })
      expect(finalUser?.creditBalance).toBe(105) // 110 - 5

      const userPurchase = await prisma.contentPurchase.findUnique({
        where: {
          userId_contentType_contentId: {
            userId: testUser.id,
            contentType: 'CHAPTER',
            contentId: testChapter.id,
          },
        },
      })
      expect(userPurchase?.accessGranted).toBe(true)

      const authorEarnings = await prisma.earning.findMany({
        where: { userId: testAuthor.id },
      })
      expect(authorEarnings).toHaveLength(1)
      expect(authorEarnings[0].authorEarning).toBe(0.35) // 70% of $0.50
    })
  })

  describe('Content Access Validation', () => {
    it('should prevent access without purchase or subscription', async () => {
      // Create a new user without credits
      const poorUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Poor User',
          role: 'READER',
          creditBalance: 0,
        },
      })

      // Check that user cannot access premium content
      const existingPurchase = await prisma.contentPurchase.findUnique({
        where: {
          userId_contentType_contentId: {
            userId: poorUser.id,
            contentType: 'CHAPTER',
            contentId: testChapter.id,
          },
        },
      })

      expect(existingPurchase).toBeNull()

      // Clean up
      await prisma.user.delete({ where: { id: poorUser.id } })
    })

    it('should allow access after credit purchase', async () => {
      // Give user credits
      await prisma.user.update({
        where: { id: testUser.id },
        data: { creditBalance: 50 },
      })

      // Purchase content
      await prisma.contentPurchase.create({
        data: {
          userId: testUser.id,
          contentType: 'NOVEL',
          contentId: testNovel.id,
          creditsSpent: testNovel.creditPrice!,
          priceAtTime: testNovel.creditPrice! * 0.10,
          accessGranted: true,
        },
      })

      // Verify access
      const purchase = await prisma.contentPurchase.findUnique({
        where: {
          userId_contentType_contentId: {
            userId: testUser.id,
            contentType: 'NOVEL',
            contentId: testNovel.id,
          },
        },
      })

      expect(purchase?.accessGranted).toBe(true)
    })
  })

  describe('Revenue Sharing Accuracy', () => {
    it('should calculate revenue splits correctly across multiple purchases', async () => {
      const purchases = [
        { credits: 5, expectedAuthorEarning: 0.35 },
        { credits: 10, expectedAuthorEarning: 0.70 },
        { credits: 25, expectedAuthorEarning: 1.75 },
      ]

      for (const purchase of purchases) {
        const dollarValue = purchase.credits * 0.10
        const authorEarning = dollarValue * 0.70
        const platformFee = dollarValue * 0.30

        expect(authorEarning).toBeCloseTo(purchase.expectedAuthorEarning, 2)
        expect(authorEarning + platformFee).toBeCloseTo(dollarValue, 2)
      }
    })
  })

  describe('Transaction Integrity', () => {
    it('should maintain balance consistency across transactions', async () => {
      // Reset user balance
      await prisma.user.update({
        where: { id: testUser.id },
        data: { creditBalance: 100 },
      })

      // Perform multiple transactions
      const transactions = [
        { type: 'SPEND', amount: -10 },
        { type: 'SPEND', amount: -5 },
        { type: 'PURCHASE', amount: 50 },
        { type: 'SPEND', amount: -15 },
      ]

      let currentBalance = 100
      for (const transaction of transactions) {
        const newBalance = currentBalance + transaction.amount

        await prisma.creditTransaction.create({
          data: {
            userId: testUser.id,
            type: transaction.type as any,
            status: 'COMPLETED',
            amount: transaction.amount,
            description: `Test transaction`,
            balanceBefore: currentBalance,
            balanceAfter: newBalance,
          },
        })

        await prisma.user.update({
          where: { id: testUser.id },
          data: { creditBalance: newBalance },
        })

        currentBalance = newBalance
      }

      // Verify final balance
      const finalUser = await prisma.user.findUnique({
        where: { id: testUser.id },
      })
      expect(finalUser?.creditBalance).toBe(120) // 100 - 10 - 5 + 50 - 15

      // Verify transaction history
      const allTransactions = await prisma.creditTransaction.findMany({
        where: { userId: testUser.id },
        orderBy: { createdAt: 'asc' },
      })

      // Check that balances are consistent
      for (let i = 0; i < allTransactions.length; i++) {
        const transaction = allTransactions[i]
        const expectedBalance = transaction.balanceBefore + transaction.amount
        expect(transaction.balanceAfter).toBe(expectedBalance)
      }
    })
  })

  describe('Error Handling', () => {
    it('should handle insufficient credits gracefully', async () => {
      // Set user balance to insufficient amount
      await prisma.user.update({
        where: { id: testUser.id },
        data: { creditBalance: 2 },
      })

      // Try to purchase content that costs 5 credits
      const user = await prisma.user.findUnique({
        where: { id: testUser.id },
      })

      expect(user?.creditBalance).toBeLessThan(5)

      // Should not be able to create purchase
      const canAfford = user!.creditBalance >= 5
      expect(canAfford).toBe(false)
    })

    it('should prevent duplicate content purchases', async () => {
      // Create initial purchase
      const purchase1 = await prisma.contentPurchase.create({
        data: {
          userId: testUser.id,
          contentType: 'CHAPTER',
          contentId: testChapter.id,
          creditsSpent: 5,
          priceAtTime: 0.50,
          accessGranted: true,
        },
      })

      // Try to create duplicate purchase (should fail due to unique constraint)
      await expect(
        prisma.contentPurchase.create({
          data: {
            userId: testUser.id,
            contentType: 'CHAPTER',
            contentId: testChapter.id,
            creditsSpent: 5,
            priceAtTime: 0.50,
            accessGranted: true,
          },
        })
      ).rejects.toThrow()
    })
  })
})
