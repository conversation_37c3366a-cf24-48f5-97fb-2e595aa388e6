/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { useSession } from 'next-auth/react'
import { CreditBalance } from '@/components/credits/credit-balance'
import { CreditPurchaseModal } from '@/components/credits/credit-purchase-modal'
import { ContentPaywall } from '@/components/credits/content-paywall'
import { creditsApi } from '@/store/api/creditsApi'
import { creditSlice } from '@/store/slices/creditSlice'

// Mock dependencies
jest.mock('next-auth/react')
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}))

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      [creditsApi.reducerPath]: creditsApi.reducer,
      credit: creditSlice.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(creditsApi.middleware),
    preloadedState: {
      credit: {
        balance: 100,
        isLoading: false,
        lastUpdated: null,
        purchaseModal: {
          isOpen: false,
          selectedPackageId: null,
        },
        notifications: {
          lowBalanceWarning: false,
          lastWarningShown: null,
        },
        preferences: {
          autoTopUpEnabled: false,
          autoTopUpThreshold: 10,
          autoTopUpPackageId: null,
          emailNotifications: true,
        },
        recentTransactions: [],
        ...initialState,
      },
    },
  })
}

const renderWithProvider = (component: React.ReactElement, store = createTestStore()) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  )
}

describe('Credit Components', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('CreditBalance', () => {
    it('should display credit balance for authenticated user', () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      renderWithProvider(<CreditBalance />)

      expect(screen.getByText(/100 credits/i)).toBeInTheDocument()
      expect(screen.getByText(/buy credits/i)).toBeInTheDocument()
    })

    it('should show sign in message for unauthenticated user', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      } as any)

      renderWithProvider(<CreditBalance />)

      expect(screen.getByText(/sign in to view credits/i)).toBeInTheDocument()
    })

    it('should show low balance warning when balance is low', () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      const store = createTestStore({
        balance: 5,
        notifications: {
          lowBalanceWarning: true,
          lastWarningShown: null,
        },
      })

      renderWithProvider(<CreditBalance />, store)

      expect(screen.getByText(/your credit balance is low/i)).toBeInTheDocument()
    })

    it('should open purchase modal when buy credits button is clicked', () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      const store = createTestStore()
      renderWithProvider(<CreditBalance />, store)

      const buyButton = screen.getByText(/buy credits/i)
      fireEvent.click(buyButton)

      const state = store.getState()
      expect(state.credit.purchaseModal.isOpen).toBe(true)
    })
  })

  describe('ContentPaywall', () => {
    const mockAccessInfo = {
      hasAccess: false,
      reason: 'no_subscription' as const,
      creditPrice: 5,
      canPurchaseWithCredits: true,
      userCreditBalance: 100,
      purchaseOptions: {
        subscription: {
          available: true,
          requiredTier: 'PREMIUM',
        },
        credits: {
          available: true,
          price: 5,
          canAfford: true,
          userBalance: 100,
        },
      },
    }

    it('should not render when user has access', () => {
      const accessInfoWithAccess = { ...mockAccessInfo, hasAccess: true }

      const { container } = renderWithProvider(
        <ContentPaywall
          contentType="CHAPTER"
          contentId="chapter1"
          contentTitle="Test Chapter"
          accessInfo={accessInfoWithAccess}
        />
      )

      expect(container.firstChild).toBeNull()
    })

    it('should show credit purchase option when available', () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      renderWithProvider(
        <ContentPaywall
          contentType="CHAPTER"
          contentId="chapter1"
          contentTitle="Test Chapter"
          accessInfo={mockAccessInfo}
        />
      )

      expect(screen.getByText(/pay with credits/i)).toBeInTheDocument()
      expect(screen.getByText(/5 credits/i)).toBeInTheDocument()
      expect(screen.getByText(/unlock for 5 credits/i)).toBeInTheDocument()
    })

    it('should show insufficient credits warning when user cannot afford', () => {
      const accessInfoInsufficientCredits = {
        ...mockAccessInfo,
        purchaseOptions: {
          ...mockAccessInfo.purchaseOptions,
          credits: {
            ...mockAccessInfo.purchaseOptions.credits,
            canAfford: false,
            userBalance: 2,
          },
        },
      }

      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      renderWithProvider(
        <ContentPaywall
          contentType="CHAPTER"
          contentId="chapter1"
          contentTitle="Test Chapter"
          accessInfo={accessInfoInsufficientCredits}
        />
      )

      expect(screen.getByText(/you need 3 more credits/i)).toBeInTheDocument()
      expect(screen.getByText(/buy more credits/i)).toBeInTheDocument()
    })

    it('should show subscription option when available', () => {
      renderWithProvider(
        <ContentPaywall
          contentType="CHAPTER"
          contentId="chapter1"
          contentTitle="Test Chapter"
          accessInfo={mockAccessInfo}
        />
      )

      expect(screen.getByText(/subscribe/i)).toBeInTheDocument()
      expect(screen.getByText(/upgrade to premium/i)).toBeInTheDocument()
    })
  })

  describe('Credit Purchase Flow', () => {
    it('should handle successful credit purchase', async () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      // Mock successful purchase
      const mockPurchase = jest.fn().mockResolvedValue({
        data: {
          purchase: {
            id: 'purchase1',
            totalCredits: 50,
            package: { name: 'Starter Pack' },
          },
        },
      })

      const store = createTestStore({
        purchaseModal: {
          isOpen: true,
          selectedPackageId: null,
        },
      })

      renderWithProvider(<CreditPurchaseModal />, store)

      // Should show the modal
      expect(screen.getByText(/purchase credits/i)).toBeInTheDocument()
    })

    it('should handle purchase errors gracefully', async () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      const store = createTestStore({
        purchaseModal: {
          isOpen: true,
          selectedPackageId: null,
        },
      })

      renderWithProvider(<CreditPurchaseModal />, store)

      // Modal should be visible
      expect(screen.getByText(/purchase credits/i)).toBeInTheDocument()
    })
  })

  describe('Credit State Management', () => {
    it('should update balance when credits are spent', () => {
      const store = createTestStore({ balance: 100 })

      // Simulate spending credits
      store.dispatch({
        type: 'credit/updateCreditBalance',
        payload: { amount: 5, type: 'subtract' },
      })

      const state = store.getState()
      expect(state.credit.balance).toBe(95)
    })

    it('should add credits when purchased', () => {
      const store = createTestStore({ balance: 50 })

      // Simulate purchasing credits
      store.dispatch({
        type: 'credit/updateCreditBalance',
        payload: { amount: 100, type: 'add' },
      })

      const state = store.getState()
      expect(state.credit.balance).toBe(150)
    })

    it('should not allow negative balance', () => {
      const store = createTestStore({ balance: 5 })

      // Try to spend more credits than available
      store.dispatch({
        type: 'credit/updateCreditBalance',
        payload: { amount: 10, type: 'subtract' },
      })

      const state = store.getState()
      expect(state.credit.balance).toBe(0) // Should not go negative
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      renderWithProvider(<CreditBalance />)

      const buyButton = screen.getByRole('button', { name: /buy credits/i })
      expect(buyButton).toBeInTheDocument()
    })

    it('should be keyboard navigable', () => {
      mockUseSession.mockReturnValue({
        data: { user: { id: 'user1', email: '<EMAIL>' } },
        status: 'authenticated',
      } as any)

      renderWithProvider(<CreditBalance />)

      const buyButton = screen.getByRole('button', { name: /buy credits/i })
      buyButton.focus()
      expect(buyButton).toHaveFocus()
    })
  })
})
