import {
  formatCredits,
  creditsToUSD,
  usdToCredits,
  calculateCreditRevenueSplit,
  getRecommendedCreditPrice,
  formatCreditPrice,
  isValidCreditPrice,
  validateCreditPurchase,
  CREDIT_CONFIG,
} from '@/lib/credits'

describe('Credit Utilities', () => {
  describe('formatCredits', () => {
    it('should format credits correctly', () => {
      expect(formatCredits(0)).toBe('0 credits')
      expect(formatCredits(1)).toBe('1 credits')
      expect(formatCredits(100)).toBe('100 credits')
      expect(formatCredits(1000)).toBe('1,000 credits')
      expect(formatCredits(1234567)).toBe('1,234,567 credits')
    })
  })

  describe('creditsToUSD', () => {
    it('should convert credits to USD correctly', () => {
      expect(creditsToUSD(0)).toBe(0)
      expect(creditsToUSD(1)).toBe(0.10)
      expect(creditsToUSD(10)).toBe(1.00)
      expect(creditsToUSD(100)).toBe(10.00)
      expect(creditsToUSD(50)).toBe(5.00)
    })
  })

  describe('usdToCredits', () => {
    it('should convert USD to credits correctly', () => {
      expect(usdToCredits(0)).toBe(0)
      expect(usdToCredits(0.10)).toBe(1)
      expect(usdToCredits(1.00)).toBe(10)
      expect(usdToCredits(10.00)).toBe(100)
      expect(usdToCredits(5.50)).toBe(55)
    })

    it('should round to nearest credit', () => {
      expect(usdToCredits(0.15)).toBe(2) // Rounds up
      expect(usdToCredits(0.14)).toBe(1) // Rounds down
      expect(usdToCredits(0.99)).toBe(10) // Rounds up
    })
  })

  describe('calculateCreditRevenueSplit', () => {
    it('should calculate revenue split correctly', () => {
      const result = calculateCreditRevenueSplit(100) // 100 credits = $10
      
      expect(result.dollarValue).toBe(10.00)
      expect(result.platformFee).toBe(3.00) // 30%
      expect(result.authorEarning).toBe(7.00) // 70%
    })

    it('should handle small amounts', () => {
      const result = calculateCreditRevenueSplit(5) // 5 credits = $0.50
      
      expect(result.dollarValue).toBe(0.50)
      expect(result.platformFee).toBe(0.15) // 30%
      expect(result.authorEarning).toBe(0.35) // 70%
    })

    it('should handle zero credits', () => {
      const result = calculateCreditRevenueSplit(0)
      
      expect(result.dollarValue).toBe(0)
      expect(result.platformFee).toBe(0)
      expect(result.authorEarning).toBe(0)
    })
  })

  describe('getRecommendedCreditPrice', () => {
    it('should return default prices for content without word count', () => {
      expect(getRecommendedCreditPrice('CHAPTER')).toBe(CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS)
      expect(getRecommendedCreditPrice('NOVEL')).toBe(CREDIT_CONFIG.DEFAULT_NOVEL_CREDITS)
    })

    it('should calculate chapter price based on word count', () => {
      expect(getRecommendedCreditPrice('CHAPTER', 500)).toBe(3) // Minimum 3 credits
      expect(getRecommendedCreditPrice('CHAPTER', 1000)).toBe(3) // 1000/500 = 2, but minimum is 3
      expect(getRecommendedCreditPrice('CHAPTER', 1500)).toBe(3) // 1500/500 = 3
      expect(getRecommendedCreditPrice('CHAPTER', 2000)).toBe(4) // 2000/500 = 4
      expect(getRecommendedCreditPrice('CHAPTER', 100)).toBe(3) // Very short, but minimum is 3
    })

    it('should calculate novel price based on word count', () => {
      expect(getRecommendedCreditPrice('NOVEL', 1000)).toBe(20) // Minimum 20 credits
      expect(getRecommendedCreditPrice('NOVEL', 50000)).toBe(50) // 50000/1000 = 50
      expect(getRecommendedCreditPrice('NOVEL', 100000)).toBe(100) // 100000/1000 = 100
      expect(getRecommendedCreditPrice('NOVEL', 500)).toBe(20) // Very short, but minimum is 20
    })
  })

  describe('formatCreditPrice', () => {
    it('should format credit price with USD equivalent', () => {
      expect(formatCreditPrice(5)).toBe('5 credits ($0.50)')
      expect(formatCreditPrice(10)).toBe('10 credits ($1.00)')
      expect(formatCreditPrice(100)).toBe('100 credits ($10.00)')
      expect(formatCreditPrice(1)).toBe('1 credits ($0.10)')
    })
  })

  describe('isValidCreditPrice', () => {
    it('should validate credit prices correctly', () => {
      expect(isValidCreditPrice(1)).toBe(true)
      expect(isValidCreditPrice(5)).toBe(true)
      expect(isValidCreditPrice(100)).toBe(true)
      expect(isValidCreditPrice(1000)).toBe(true)
      
      expect(isValidCreditPrice(0)).toBe(false) // Zero not allowed
      expect(isValidCreditPrice(-1)).toBe(false) // Negative not allowed
      expect(isValidCreditPrice(1001)).toBe(false) // Over maximum
      expect(isValidCreditPrice(5.5)).toBe(false) // Not integer
    })
  })

  describe('validateCreditPurchase', () => {
    it('should validate successful purchase', () => {
      const result = validateCreditPurchase(100, 50)
      expect(result.canPurchase).toBe(true)
      expect(result.shortfall).toBeUndefined()
    })

    it('should validate exact balance purchase', () => {
      const result = validateCreditPurchase(50, 50)
      expect(result.canPurchase).toBe(true)
      expect(result.shortfall).toBeUndefined()
    })

    it('should identify insufficient balance', () => {
      const result = validateCreditPurchase(30, 50)
      expect(result.canPurchase).toBe(false)
      expect(result.shortfall).toBe(20)
    })

    it('should handle zero balance', () => {
      const result = validateCreditPurchase(0, 10)
      expect(result.canPurchase).toBe(false)
      expect(result.shortfall).toBe(10)
    })

    it('should handle zero cost', () => {
      const result = validateCreditPurchase(10, 0)
      expect(result.canPurchase).toBe(true)
      expect(result.shortfall).toBeUndefined()
    })
  })

  describe('CREDIT_CONFIG constants', () => {
    it('should have correct configuration values', () => {
      expect(CREDIT_CONFIG.CREDIT_TO_USD_RATE).toBe(0.10)
      expect(CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS).toBe(5)
      expect(CREDIT_CONFIG.DEFAULT_NOVEL_CREDITS).toBe(50)
      expect(CREDIT_CONFIG.PLATFORM_PERCENTAGE).toBe(30)
      expect(CREDIT_CONFIG.AUTHOR_PERCENTAGE).toBe(70)
    })

    it('should have consistent revenue split percentages', () => {
      const total = CREDIT_CONFIG.PLATFORM_PERCENTAGE + CREDIT_CONFIG.AUTHOR_PERCENTAGE
      expect(total).toBe(100)
    })
  })

  describe('Edge cases', () => {
    it('should handle very large numbers', () => {
      const largeCredits = 999999
      const usdValue = creditsToUSD(largeCredits)
      expect(usdValue).toBe(99999.90)
      
      const backToCredits = usdToCredits(usdValue)
      expect(backToCredits).toBe(largeCredits)
    })

    it('should handle floating point precision', () => {
      // Test that we don't have floating point precision issues
      const credits = 33
      const usd = creditsToUSD(credits)
      const backToCredits = usdToCredits(usd)
      expect(backToCredits).toBe(credits)
    })

    it('should handle revenue split precision', () => {
      // Test that revenue split doesn't lose money due to rounding
      const credits = 7 // Odd number that might cause rounding issues
      const split = calculateCreditRevenueSplit(credits)
      
      const total = split.platformFee + split.authorEarning
      expect(total).toBeCloseTo(split.dollarValue, 2)
    })
  })

  describe('Performance', () => {
    it('should handle bulk calculations efficiently', () => {
      const start = performance.now()
      
      // Perform 1000 calculations
      for (let i = 0; i < 1000; i++) {
        creditsToUSD(i)
        usdToCredits(i * 0.10)
        calculateCreditRevenueSplit(i)
      }
      
      const end = performance.now()
      const duration = end - start
      
      // Should complete in reasonable time (less than 100ms)
      expect(duration).toBeLessThan(100)
    })
  })
})
