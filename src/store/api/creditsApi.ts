import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { 
  CreditTransaction, 
  CreditPurchase, 
  CreditPackage,
  ContentPurchase,
  PaymentStatus,
  CreditTransactionType,
  CreditTransactionStatus,
  ContentType
} from '@prisma/client'

// Types
export interface CreditBalance {
  balance: number
  userId: string
}

export interface CreditTransactionWithDetails extends CreditTransaction {
  contentPurchase?: {
    id: string
    contentType: ContentType
    contentId: string
  }
}

export interface CreditTransactionsResponse {
  transactions: CreditTransactionWithDetails[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface CreditPackagesResponse {
  packages: CreditPackage[]
}

export interface CreditPurchaseWithPackage extends CreditPurchase {
  package: CreditPackage
}

export interface PurchaseCreditsRequest {
  packageId: string
  paymentMethodId: string
}

export interface PurchaseCreditsResponse {
  purchase: CreditPurchaseWithPackage
  clientSecret?: string
  requiresAction?: boolean
}

export interface SpendCreditsRequest {
  contentType: 'NOVEL' | 'CHAPTER'
  contentId: string
}

export interface SpendCreditsResponse {
  message: string
  purchase: ContentPurchase
  newBalance: number
  creditsSpent: number
}

export interface CreditTransactionsQueryParams {
  page?: number
  limit?: number
  type?: CreditTransactionType
  startDate?: string
  endDate?: string
}

export interface EarningSummary {
  summary: {
    totalEarnings: number
    totalRevenue: number
    unpaidAmount: number
    totalTransactions: number
    period: {
      days: number
      earnings: number
      revenue: number
      transactions: number
    }
  }
  creditMetrics: {
    totalCreditTransactions: number
    totalCreditRevenue: number
    totalCreditEarnings: number
    averageCreditTransaction: number
    creditEarningsThisPeriod: number
  }
  byType: {
    allTime: Record<string, any>
    period: Record<string, any>
  }
  unpaidEarnings: any[]
  recentPayouts: any[]
  monthlyTrends: any[]
}

export const creditsApi = createApi({
  reducerPath: 'creditsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/credits',
    credentials: 'include',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json')
      return headers
    },
  }),
  tagTypes: ['CreditBalance', 'CreditTransactions', 'CreditPackages', 'CreditPurchases', 'EarningSummary'],
  endpoints: (builder) => ({
    // Get user's credit balance
    getCreditBalance: builder.query<CreditBalance, void>({
      query: () => '/balance',
      providesTags: ['CreditBalance'],
    }),

    // Get credit transaction history
    getCreditTransactions: builder.query<CreditTransactionsResponse, CreditTransactionsQueryParams>({
      query: (params = {}) => ({
        url: '/transactions',
        params: {
          page: params.page?.toString(),
          limit: params.limit?.toString(),
          type: params.type,
          startDate: params.startDate,
          endDate: params.endDate,
        },
      }),
      providesTags: ['CreditTransactions'],
    }),

    // Get available credit packages
    getCreditPackages: builder.query<CreditPackagesResponse, void>({
      query: () => '/packages',
      providesTags: ['CreditPackages'],
    }),

    // Purchase credits
    purchaseCredits: builder.mutation<PurchaseCreditsResponse, PurchaseCreditsRequest>({
      query: (data) => ({
        url: '/purchase',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditBalance', 'CreditTransactions', 'CreditPurchases'],
    }),

    // Spend credits on content
    spendCredits: builder.mutation<SpendCreditsResponse, SpendCreditsRequest>({
      query: (data) => ({
        url: '/spend',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditBalance', 'CreditTransactions'],
    }),

    // Get earnings summary (for authors)
    getEarningSummary: builder.query<EarningSummary, { period?: string }>({
      query: (params = {}) => ({
        url: '/earnings/summary',
        params: {
          period: params.period || '30',
        },
      }),
      providesTags: ['EarningSummary'],
    }),

    // Admin endpoints
    createCreditPackage: builder.mutation<CreditPackage, Omit<CreditPackage, 'id' | 'createdAt' | 'updatedAt'>>({
      query: (data) => ({
        url: '/packages',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditPackages'],
    }),

    updateCreditPackage: builder.mutation<CreditPackage, { id: string; data: Partial<CreditPackage> }>({
      query: ({ id, data }) => ({
        url: `/packages/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['CreditPackages'],
    }),

    deleteCreditPackage: builder.mutation<void, string>({
      query: (id) => ({
        url: `/packages/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['CreditPackages'],
    }),
  }),
})

export const {
  useGetCreditBalanceQuery,
  useGetCreditTransactionsQuery,
  useGetCreditPackagesQuery,
  usePurchaseCreditsMutation,
  useSpendCreditsMutation,
  useGetEarningSummaryQuery,
  useCreateCreditPackageMutation,
  useUpdateCreditPackageMutation,
  useDeleteCreditPackageMutation,
} = creditsApi
