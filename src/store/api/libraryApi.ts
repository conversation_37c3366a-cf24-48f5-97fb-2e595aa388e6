import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Library } from '@prisma/client'
import type { NovelWithAuthor } from './novelsApi'

export interface LibraryWithNovel extends Library {
  novel: NovelWithAuthor
}

export const libraryApi = createApi({
  reducerPath: 'libraryApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/library',
    credentials: 'include',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json')
      return headers
    },
  }),
  tagTypes: ['Library'],
  endpoints: (builder) => ({
    getLibrary: builder.query<LibraryWithNovel[], void>({
      query: () => '',
      providesTags: ['Library'],
    }),

    addToLibrary: builder.mutation<Library, string>({
      query: (novelId) => ({
        url: '',
        method: 'POST',
        body: { novelId },
      }),
      invalidatesTags: ['Library'],
    }),

    removeFromLibrary: builder.mutation<void, string>({
      query: (novelId) => ({
        url: `/${novelId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Library'],
    }),

    checkInLibrary: builder.query<{ inLibrary: boolean }, string>({
      query: (novelId) => `/check/${novelId}`,
      providesTags: (result, error, novelId) => [
        { type: 'Library', id: novelId },
      ],
    }),
  }),
})

export const {
  useGetLibraryQuery,
  useAddToLibraryMutation,
  useRemoveFromLibraryMutation,
  useCheckInLibraryQuery,
} = libraryApi