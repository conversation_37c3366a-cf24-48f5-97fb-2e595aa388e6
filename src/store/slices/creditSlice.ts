import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { RootState } from '@/lib/store'

// Types
export interface CreditState {
  balance: number
  isLoading: boolean
  lastUpdated: string | null
  purchaseModal: {
    isOpen: boolean
    selectedPackageId: string | null
  }
  notifications: {
    lowBalanceWarning: boolean
    lastWarningShown: string | null
  }
  preferences: {
    autoTopUpEnabled: boolean
    autoTopUpThreshold: number
    autoTopUpPackageId: string | null
    emailNotifications: boolean
  }
  recentTransactions: Array<{
    id: string
    type: string
    amount: number
    description: string
    createdAt: string
  }>
}

// Initial state
const initialState: CreditState = {
  balance: 0,
  isLoading: false,
  lastUpdated: null,
  purchaseModal: {
    isOpen: false,
    selectedPackageId: null,
  },
  notifications: {
    lowBalanceWarning: false,
    lastWarningShown: null,
  },
  preferences: {
    autoTopUpEnabled: false,
    autoTopUpThreshold: 10,
    autoTopUpPackageId: null,
    emailNotifications: true,
  },
  recentTransactions: [],
}

// Slice
export const creditSlice = createSlice({
  name: 'credit',
  initialState,
  reducers: {
    // Balance management
    setCreditBalance: (state, action: PayloadAction<number>) => {
      state.balance = action.payload
      state.lastUpdated = new Date().toISOString()
    },

    updateCreditBalance: (state, action: PayloadAction<{ amount: number; type: 'add' | 'subtract' }>) => {
      const { amount, type } = action.payload
      if (type === 'add') {
        state.balance += amount
      } else {
        state.balance = Math.max(0, state.balance - amount)
      }
      state.lastUpdated = new Date().toISOString()
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },

    // Purchase modal management
    openPurchaseModal: (state, action: PayloadAction<string | null>) => {
      state.purchaseModal.isOpen = true
      state.purchaseModal.selectedPackageId = action.payload
    },

    closePurchaseModal: (state) => {
      state.purchaseModal.isOpen = false
      state.purchaseModal.selectedPackageId = null
    },

    setSelectedPackage: (state, action: PayloadAction<string | null>) => {
      state.purchaseModal.selectedPackageId = action.payload
    },

    // Notifications
    setLowBalanceWarning: (state, action: PayloadAction<boolean>) => {
      state.notifications.lowBalanceWarning = action.payload
      if (action.payload) {
        state.notifications.lastWarningShown = new Date().toISOString()
      }
    },

    dismissLowBalanceWarning: (state) => {
      state.notifications.lowBalanceWarning = false
    },

    // Preferences
    updatePreferences: (state, action: PayloadAction<Partial<CreditState['preferences']>>) => {
      state.preferences = { ...state.preferences, ...action.payload }
    },

    setAutoTopUp: (state, action: PayloadAction<{ enabled: boolean; threshold?: number; packageId?: string }>) => {
      const { enabled, threshold, packageId } = action.payload
      state.preferences.autoTopUpEnabled = enabled
      if (threshold !== undefined) {
        state.preferences.autoTopUpThreshold = threshold
      }
      if (packageId !== undefined) {
        state.preferences.autoTopUpPackageId = packageId
      }
    },

    // Recent transactions
    addRecentTransaction: (state, action: PayloadAction<CreditState['recentTransactions'][0]>) => {
      state.recentTransactions.unshift(action.payload)
      // Keep only the last 10 transactions
      state.recentTransactions = state.recentTransactions.slice(0, 10)
    },

    setRecentTransactions: (state, action: PayloadAction<CreditState['recentTransactions']>) => {
      state.recentTransactions = action.payload
    },

    clearRecentTransactions: (state) => {
      state.recentTransactions = []
    },

    // Reset state
    resetCreditState: () => initialState,
  },
})

// Actions
export const {
  setCreditBalance,
  updateCreditBalance,
  setLoading,
  openPurchaseModal,
  closePurchaseModal,
  setSelectedPackage,
  setLowBalanceWarning,
  dismissLowBalanceWarning,
  updatePreferences,
  setAutoTopUp,
  addRecentTransaction,
  setRecentTransactions,
  clearRecentTransactions,
  resetCreditState,
} = creditSlice.actions

// Selectors
export const selectCreditBalance = (state: RootState) => state.credit.balance
export const selectCreditLoading = (state: RootState) => state.credit.isLoading
export const selectCreditLastUpdated = (state: RootState) => state.credit.lastUpdated
export const selectPurchaseModal = (state: RootState) => state.credit.purchaseModal
export const selectCreditNotifications = (state: RootState) => state.credit.notifications
export const selectCreditPreferences = (state: RootState) => state.credit.preferences
export const selectRecentTransactions = (state: RootState) => state.credit.recentTransactions

// Complex selectors
export const selectShouldShowLowBalanceWarning = (state: RootState) => {
  const { balance } = state.credit
  const { lowBalanceWarning, lastWarningShown } = state.credit.notifications
  const { autoTopUpThreshold } = state.credit.preferences
  
  // Show warning if balance is below threshold and we haven't shown it recently
  const shouldShow = balance <= autoTopUpThreshold && !lowBalanceWarning
  const lastWarning = lastWarningShown ? new Date(lastWarningShown) : null
  const now = new Date()
  const hoursSinceLastWarning = lastWarning ? (now.getTime() - lastWarning.getTime()) / (1000 * 60 * 60) : Infinity
  
  return shouldShow && hoursSinceLastWarning > 24 // Show at most once per day
}

export const selectCanAffordContent = (state: RootState, creditPrice: number) => {
  return state.credit.balance >= creditPrice
}

export const selectCreditShortfall = (state: RootState, creditPrice: number) => {
  const shortfall = creditPrice - state.credit.balance
  return shortfall > 0 ? shortfall : 0
}

// Thunks for complex operations
export const checkAndShowLowBalanceWarning = () => (dispatch: any, getState: any) => {
  const state = getState() as RootState
  if (selectShouldShowLowBalanceWarning(state)) {
    dispatch(setLowBalanceWarning(true))
  }
}

export const handleSuccessfulPurchase = (credits: number, transaction: any) => (dispatch: any) => {
  dispatch(updateCreditBalance({ amount: credits, type: 'add' }))
  dispatch(addRecentTransaction({
    id: transaction.id,
    type: 'PURCHASE',
    amount: credits,
    description: transaction.description,
    createdAt: transaction.createdAt,
  }))
  dispatch(closePurchaseModal())
  dispatch(dismissLowBalanceWarning())
}

export const handleSuccessfulSpend = (credits: number, transaction: any) => (dispatch: any) => {
  dispatch(updateCreditBalance({ amount: credits, type: 'subtract' }))
  dispatch(addRecentTransaction({
    id: transaction.id,
    type: 'SPEND',
    amount: -credits,
    description: transaction.description,
    createdAt: transaction.createdAt,
  }))
  dispatch(checkAndShowLowBalanceWarning())
}

export default creditSlice.reducer
