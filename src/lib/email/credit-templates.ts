import { formatCredits, creditsToUSD } from '@/lib/credits'
import { formatCurrency } from '@/lib/stripe'

export interface CreditPurchaseEmailData {
  userName: string
  packageName: string
  credits: number
  bonusCredits: number
  totalCredits: number
  amount: number
  transactionId: string
  purchaseDate: string
}

export interface LowBalanceEmailData {
  userName: string
  currentBalance: number
  threshold: number
  recommendedPackages: Array<{
    name: string
    credits: number
    price: number
  }>
}

export interface ContentUnlockEmailData {
  userName: string
  contentTitle: string
  contentType: 'NOVEL' | 'CHAPTER'
  creditsSpent: number
  remainingBalance: number
  authorName: string
}

export interface WriterEarningsEmailData {
  authorName: string
  contentTitle: string
  contentType: 'NOVEL' | 'CHAPTER'
  creditsEarned: number
  dollarAmount: number
  buyerName?: string
  totalEarnings: number
  unpaidBalance: number
}

export const creditPurchaseEmailTemplate = (data: CreditPurchaseEmailData) => {
  const subject = `Credit Purchase Confirmation - ${formatCredits(data.totalCredits)} Added!`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Credit Purchase Confirmation</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px; }
        .content { background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef; }
        .highlight { background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .credits { font-size: 24px; font-weight: bold; color: #1976d2; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        .button { display: inline-block; background: #1976d2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎉 Credit Purchase Successful!</h1>
          <p>Thank you for your purchase, ${data.userName}!</p>
        </div>
        
        <div class="content">
          <div class="highlight">
            <h2>Purchase Summary</h2>
            <p><strong>Package:</strong> ${data.packageName}</p>
            <p><strong>Credits:</strong> <span class="credits">${formatCredits(data.credits)}</span></p>
            ${data.bonusCredits > 0 ? `<p><strong>Bonus Credits:</strong> <span class="credits">+${formatCredits(data.bonusCredits)}</span></p>` : ''}
            <p><strong>Total Credits Added:</strong> <span class="credits">${formatCredits(data.totalCredits)}</span></p>
            <p><strong>Amount Paid:</strong> ${formatCurrency(data.amount)}</p>
            <p><strong>Transaction ID:</strong> ${data.transactionId}</p>
            <p><strong>Date:</strong> ${new Date(data.purchaseDate).toLocaleDateString()}</p>
          </div>
          
          <h3>What's Next?</h3>
          <p>Your credits have been added to your account and are ready to use! You can now:</p>
          <ul>
            <li>Unlock premium novels and chapters</li>
            <li>Support your favorite authors</li>
            <li>Access exclusive content</li>
          </ul>
          
          <div style="text-align: center;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" class="button">View Your Credits</a>
          </div>
          
          <p><strong>Remember:</strong> Your credits never expire, so you can use them whenever you want!</p>
        </div>
        
        <div class="footer">
          <p>Thank you for supporting our authors and platform!</p>
          <p>If you have any questions, please contact our support team.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Credit Purchase Confirmation
    
    Hi ${data.userName},
    
    Your credit purchase was successful!
    
    Purchase Summary:
    - Package: ${data.packageName}
    - Credits: ${formatCredits(data.credits)}
    ${data.bonusCredits > 0 ? `- Bonus Credits: +${formatCredits(data.bonusCredits)}` : ''}
    - Total Credits Added: ${formatCredits(data.totalCredits)}
    - Amount Paid: ${formatCurrency(data.amount)}
    - Transaction ID: ${data.transactionId}
    - Date: ${new Date(data.purchaseDate).toLocaleDateString()}
    
    Your credits are now available in your account and ready to use!
    
    Visit ${process.env.NEXTAUTH_URL}/dashboard to view your credits.
    
    Thank you for supporting our authors and platform!
  `
  
  return { subject, html, text }
}

export const lowBalanceEmailTemplate = (data: LowBalanceEmailData) => {
  const subject = `Low Credit Balance - Time to Top Up!`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Low Credit Balance</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #fff3cd; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px; border: 1px solid #ffeaa7; }
        .content { background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef; }
        .package { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 10px 0; border-left: 4px solid #1976d2; }
        .balance { font-size: 20px; font-weight: bold; color: #e67e22; }
        .button { display: inline-block; background: #1976d2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>⚠️ Low Credit Balance</h1>
          <p>Hi ${data.userName}, your credits are running low!</p>
        </div>
        
        <div class="content">
          <p>Your current balance: <span class="balance">${formatCredits(data.currentBalance)}</span></p>
          <p>You're below your threshold of ${formatCredits(data.threshold)}.</p>
          
          <p>Don't miss out on great content! Here are some popular credit packages:</p>
          
          ${data.recommendedPackages.map(pkg => `
            <div class="package">
              <h3>${pkg.name}</h3>
              <p><strong>${formatCredits(pkg.credits)}</strong> for <strong>${formatCurrency(pkg.price)}</strong></p>
            </div>
          `).join('')}
          
          <div style="text-align: center; margin-top: 20px;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard/credits" class="button">Buy Credits Now</a>
            <a href="${process.env.NEXTAUTH_URL}/pricing" class="button" style="background: #28a745;">View Subscriptions</a>
          </div>
        </div>
        
        <div class="footer">
          <p>You can disable these notifications in your account settings.</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Low Credit Balance
    
    Hi ${data.userName},
    
    Your credit balance is running low!
    
    Current balance: ${formatCredits(data.currentBalance)}
    Threshold: ${formatCredits(data.threshold)}
    
    Recommended packages:
    ${data.recommendedPackages.map(pkg => `- ${pkg.name}: ${formatCredits(pkg.credits)} for ${formatCurrency(pkg.price)}`).join('\n')}
    
    Buy more credits: ${process.env.NEXTAUTH_URL}/dashboard/credits
    View subscriptions: ${process.env.NEXTAUTH_URL}/pricing
  `
  
  return { subject, html, text }
}

export const contentUnlockEmailTemplate = (data: ContentUnlockEmailData) => {
  const subject = `Content Unlocked: ${data.contentTitle}`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Content Unlocked</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #d4edda; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px; border: 1px solid #c3e6cb; }
        .content { background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef; }
        .highlight { background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .button { display: inline-block; background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🔓 Content Unlocked!</h1>
          <p>Enjoy your new ${data.contentType.toLowerCase()}, ${data.userName}!</p>
        </div>
        
        <div class="content">
          <div class="highlight">
            <h2>${data.contentTitle}</h2>
            <p><strong>By:</strong> ${data.authorName}</p>
            <p><strong>Type:</strong> ${data.contentType}</p>
            <p><strong>Credits Spent:</strong> ${formatCredits(data.creditsSpent)}</p>
            <p><strong>Remaining Balance:</strong> ${formatCredits(data.remainingBalance)}</p>
          </div>
          
          <p>Your content is now available in your library and ready to read!</p>
          
          <div style="text-align: center;">
            <a href="${process.env.NEXTAUTH_URL}/library" class="button">Go to Library</a>
          </div>
          
          <p>Thank you for supporting ${data.authorName} and our platform!</p>
        </div>
        
        <div class="footer">
          <p>Happy reading! 📚</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    Content Unlocked!
    
    Hi ${data.userName},
    
    You've successfully unlocked: ${data.contentTitle}
    By: ${data.authorName}
    Type: ${data.contentType}
    
    Credits spent: ${formatCredits(data.creditsSpent)}
    Remaining balance: ${formatCredits(data.remainingBalance)}
    
    Your content is now available in your library!
    
    Visit: ${process.env.NEXTAUTH_URL}/library
    
    Thank you for supporting ${data.authorName}!
  `
  
  return { subject, html, text }
}

export const writerEarningsEmailTemplate = (data: WriterEarningsEmailData) => {
  const subject = `New Earnings: ${formatCurrency(data.dollarAmount)} from Credit Purchase!`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Earnings</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #d1ecf1; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px; border: 1px solid #bee5eb; }
        .content { background: white; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef; }
        .earnings { background: #d4edda; padding: 15px; border-radius: 6px; margin: 15px 0; text-align: center; }
        .amount { font-size: 24px; font-weight: bold; color: #155724; }
        .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>💰 New Earnings!</h1>
          <p>Congratulations ${data.authorName}, you've earned money!</p>
        </div>
        
        <div class="content">
          <div class="earnings">
            <h2>You Earned</h2>
            <div class="amount">${formatCurrency(data.dollarAmount)}</div>
            <p>From ${formatCredits(data.creditsEarned)} credit purchase</p>
          </div>
          
          <h3>Purchase Details</h3>
          <p><strong>Content:</strong> ${data.contentTitle}</p>
          <p><strong>Type:</strong> ${data.contentType}</p>
          ${data.buyerName ? `<p><strong>Reader:</strong> ${data.buyerName}</p>` : ''}
          
          <h3>Your Earnings Summary</h3>
          <p><strong>This Purchase:</strong> ${formatCurrency(data.dollarAmount)} (70% of ${formatCurrency(data.dollarAmount / 0.7)})</p>
          <p><strong>Total Earnings:</strong> ${formatCurrency(data.totalEarnings)}</p>
          <p><strong>Unpaid Balance:</strong> ${formatCurrency(data.unpaidBalance)}</p>
          
          <div style="text-align: center;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard/earnings" class="button">View Earnings Dashboard</a>
          </div>
          
          <p><strong>Payout Info:</strong> Earnings are paid out monthly when your balance reaches $50 or more.</p>
        </div>
        
        <div class="footer">
          <p>Keep creating great content! 🚀</p>
        </div>
      </div>
    </body>
    </html>
  `
  
  const text = `
    New Earnings!
    
    Congratulations ${data.authorName}!
    
    You earned ${formatCurrency(data.dollarAmount)} from a credit purchase!
    
    Purchase Details:
    - Content: ${data.contentTitle}
    - Type: ${data.contentType}
    - Credits: ${formatCredits(data.creditsEarned)}
    ${data.buyerName ? `- Reader: ${data.buyerName}` : ''}
    
    Your Earnings:
    - This Purchase: ${formatCurrency(data.dollarAmount)}
    - Total Earnings: ${formatCurrency(data.totalEarnings)}
    - Unpaid Balance: ${formatCurrency(data.unpaidBalance)}
    
    View your earnings: ${process.env.NEXTAUTH_URL}/dashboard/earnings
    
    Payouts are processed monthly when your balance reaches $50+.
    
    Keep creating great content!
  `
  
  return { subject, html, text }
}
