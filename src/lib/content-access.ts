import { SubscriptionTier, SubscriptionStatus, ContentType } from "@prisma/client"
import type { User, Subscription, Novel, Chapter, ContentPurchase } from "@prisma/client"

export interface UserWithSubscription extends User {
  subscriptions: Subscription[]
  contentPurchases?: ContentPurchase[]
}

export interface ContentAccessResult {
  hasAccess: boolean
  reason?: 'not_premium' | 'insufficient_tier' | 'no_subscription' | 'subscription_inactive' | 'insufficient_credits'
  requiredTier?: SubscriptionTier
  currentTier?: SubscriptionTier
  accessMethod?: 'free' | 'subscription' | 'credit_purchase'
  creditPrice?: number
  canPurchaseWithCredits?: boolean
  userCreditBalance?: number
}

/**
 * Check if a user has access to premium content based on their subscription or credit purchases
 */
export function checkContentAccess(
  user: UserWithSubscription | null,
  content: {
    isPremium: boolean;
    requiredTier?: SubscriptionTier | null;
    creditPrice?: number | null;
    id?: string;
    contentType?: ContentType;
  }
): ContentAccessResult {
  // If content is not premium, everyone has access
  if (!content.isPremium) {
    return { hasAccess: true, accessMethod: 'free' }
  }

  // If user is not logged in, check if credit purchase is available
  if (!user) {
    return {
      hasAccess: false,
      reason: 'no_subscription',
      requiredTier: content.requiredTier || SubscriptionTier.PREMIUM,
      creditPrice: content.creditPrice || undefined,
      canPurchaseWithCredits: !!(content.creditPrice && content.creditPrice > 0)
    }
  }

  // Check if user has already purchased this content with credits
  if (content.id && content.contentType && user.contentPurchases) {
    const existingPurchase = user.contentPurchases.find(purchase =>
      purchase.contentType === content.contentType &&
      purchase.contentId === content.id &&
      purchase.accessGranted
    )

    if (existingPurchase) {
      return { hasAccess: true, accessMethod: 'credit_purchase' }
    }
  }

  // Get user's active subscription
  const activeSubscription = user.subscriptions.find(sub =>
    sub.status === SubscriptionStatus.ACTIVE || sub.status === SubscriptionStatus.TRIALING
  )

  // If no active subscription, check if credit purchase is available
  if (!activeSubscription) {
    const canPurchaseWithCredits = !!(content.creditPrice && content.creditPrice > 0)
    const hasEnoughCredits = canPurchaseWithCredits && user.creditBalance >= content.creditPrice!

    return {
      hasAccess: false,
      reason: 'no_subscription',
      requiredTier: content.requiredTier || SubscriptionTier.PREMIUM,
      creditPrice: content.creditPrice || undefined,
      canPurchaseWithCredits,
      userCreditBalance: user.creditBalance
    }
  }

  // Check if subscription tier is sufficient
  const requiredTier = content.requiredTier || SubscriptionTier.PREMIUM
  const userTier = activeSubscription.tier

  // Define tier hierarchy
  const tierHierarchy = {
    [SubscriptionTier.FREE]: 0,
    [SubscriptionTier.PREMIUM]: 1,
    [SubscriptionTier.PREMIUM_PLUS]: 2,
  }

  const userTierLevel = tierHierarchy[userTier]
  const requiredTierLevel = tierHierarchy[requiredTier]

  if (userTierLevel < requiredTierLevel) {
    const canPurchaseWithCredits = !!(content.creditPrice && content.creditPrice > 0)

    return {
      hasAccess: false,
      reason: 'insufficient_tier',
      requiredTier,
      currentTier: userTier,
      creditPrice: content.creditPrice || undefined,
      canPurchaseWithCredits,
      userCreditBalance: user.creditBalance
    }
  }

  return { hasAccess: true, accessMethod: 'subscription', currentTier: userTier }
}

/**
 * Check if a user can access a specific novel
 */
export function checkNovelAccess(
  user: UserWithSubscription | null,
  novel: Novel
): ContentAccessResult {
  return checkContentAccess(user, {
    isPremium: novel.isPremium,
    requiredTier: novel.requiredTier,
    creditPrice: novel.creditPrice,
    id: novel.id,
    contentType: ContentType.NOVEL
  })
}

/**
 * Check if a user can access a specific chapter
 */
export function checkChapterAccess(
  user: UserWithSubscription | null,
  chapter: Chapter,
  novel?: Novel
): ContentAccessResult {
  // First check chapter-level restrictions
  const chapterAccess = checkContentAccess(user, {
    isPremium: chapter.isPremium,
    requiredTier: chapter.requiredTier,
    creditPrice: chapter.creditPrice,
    id: chapter.id,
    contentType: ContentType.CHAPTER
  })

  // If chapter access is granted, return that result
  if (chapterAccess.hasAccess) {
    return chapterAccess
  }

  // If novel is provided, also check novel-level restrictions
  if (novel) {
    const novelAccess = checkContentAccess(user, {
      isPremium: novel.isPremium,
      requiredTier: novel.requiredTier,
      creditPrice: novel.creditPrice,
      id: novel.id,
      contentType: ContentType.NOVEL
    })

    // If novel access is granted, user can access chapter
    if (novelAccess.hasAccess) {
      return novelAccess
    }

    // If both chapter and novel access are denied, return the more specific error
    // Prefer chapter-level access info if it has credit purchase options
    if (chapterAccess.canPurchaseWithCredits && !novelAccess.canPurchaseWithCredits) {
      return chapterAccess
    }

    return novelAccess
  }

  return chapterAccess
}

/**
 * Check if a user has purchased specific content with credits
 */
export function hasContentPurchase(
  user: UserWithSubscription | null,
  contentType: ContentType,
  contentId: string
): boolean {
  if (!user?.contentPurchases) return false

  return user.contentPurchases.some(purchase =>
    purchase.contentType === contentType &&
    purchase.contentId === contentId &&
    purchase.accessGranted &&
    (!purchase.expiresAt || purchase.expiresAt > new Date())
  )
}

/**
 * Get comprehensive access information for content including all access methods
 */
export function getContentAccessInfo(
  user: UserWithSubscription | null,
  content: {
    isPremium: boolean;
    requiredTier?: SubscriptionTier | null;
    creditPrice?: number | null;
    id: string;
    contentType: ContentType;
  }
) {
  const accessResult = checkContentAccess(user, content)
  const hasPurchased = hasContentPurchase(user, content.contentType, content.id)

  return {
    ...accessResult,
    hasPurchased,
    purchaseOptions: {
      subscription: {
        available: !accessResult.hasAccess && accessResult.reason === 'no_subscription',
        requiredTier: accessResult.requiredTier
      },
      credits: {
        available: !!(content.creditPrice && content.creditPrice > 0),
        price: content.creditPrice,
        canAfford: user ? user.creditBalance >= (content.creditPrice || 0) : false,
        userBalance: user?.creditBalance || 0
      }
    }
  }
}

/**
 * Get the display name for a subscription tier
 */
export function getTierDisplayName(tier: SubscriptionTier): string {
  switch (tier) {
    case SubscriptionTier.FREE:
      return 'Free'
    case SubscriptionTier.PREMIUM:
      return 'Premium'
    case SubscriptionTier.PREMIUM_PLUS:
      return 'Premium Plus'
    default:
      return tier
  }
}

/**
 * Get the access reason message for display
 */
export function getAccessReasonMessage(result: ContentAccessResult): string {
  if (result.hasAccess) {
    return 'You have access to this content'
  }

  switch (result.reason) {
    case 'no_subscription':
      return `This content requires a ${getTierDisplayName(result.requiredTier!)} subscription`
    case 'insufficient_tier':
      return `This content requires ${getTierDisplayName(result.requiredTier!)} or higher. You currently have ${getTierDisplayName(result.currentTier!)}`
    case 'subscription_inactive':
      return 'Your subscription is not active. Please check your billing information'
    default:
      return 'You do not have access to this premium content'
  }
}

/**
 * Check if user is an author of the content (authors can always access their own content)
 */
export function isContentAuthor(user: User | null, authorId: string): boolean {
  return user?.id === authorId
}
