// Credit system configuration and utilities

export const CREDIT_CONFIG = {
  // Credit to USD conversion rate (1 credit = $0.10)
  CREDIT_TO_USD_RATE: 0.10,
  
  // Default credit prices for content
  DEFAULT_CHAPTER_CREDITS: 5,  // 5 credits = $0.50
  DEFAULT_NOVEL_CREDITS: 50,   // 50 credits = $5.00
  
  // Revenue sharing for credit purchases
  PLATFORM_PERCENTAGE: 30,
  AUTHOR_PERCENTAGE: 70,
} as const

// Default credit packages
export const DEFAULT_CREDIT_PACKAGES = [
  {
    name: "Starter Pack",
    description: "Perfect for trying out premium content",
    credits: 50,
    price: 4.99,
    bonusCredits: 0,
    sortOrder: 1,
  },
  {
    name: "Value Pack",
    description: "Great value for regular readers",
    credits: 120,
    price: 9.99,
    bonusCredits: 10, // 10% bonus
    sortOrder: 2,
  },
  {
    name: "Premium Pack",
    description: "Best value for avid readers",
    credits: 300,
    price: 19.99,
    bonusCredits: 50, // 16% bonus
    sortOrder: 3,
  },
  {
    name: "Ultimate Pack",
    description: "Maximum value for power readers",
    credits: 600,
    price: 34.99,
    bonusCredits: 150, // 25% bonus
    sortOrder: 4,
  },
] as const

// Helper functions
export const formatCredits = (credits: number): string => {
  return `${credits.toLocaleString()} credits`
}

export const creditsToUSD = (credits: number): number => {
  return credits * CREDIT_CONFIG.CREDIT_TO_USD_RATE
}

export const usdToCredits = (usd: number): number => {
  return Math.round(usd / CREDIT_CONFIG.CREDIT_TO_USD_RATE)
}

export const calculateCreditRevenueSplit = (credits: number) => {
  const dollarValue = creditsToUSD(credits)
  const platformFee = (dollarValue * CREDIT_CONFIG.PLATFORM_PERCENTAGE) / 100
  const authorEarning = dollarValue - platformFee
  
  return {
    dollarValue,
    platformFee,
    authorEarning,
  }
}

// Content pricing helpers
export const getRecommendedCreditPrice = (contentType: 'NOVEL' | 'CHAPTER', wordCount?: number): number => {
  if (contentType === 'CHAPTER') {
    // Base price for chapters, can be adjusted based on word count
    if (wordCount) {
      // 1 credit per 500 words, minimum 3 credits
      return Math.max(3, Math.ceil(wordCount / 500))
    }
    return CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS
  } else {
    // Base price for novels, can be adjusted based on total word count or chapter count
    if (wordCount) {
      // 1 credit per 1000 words, minimum 20 credits
      return Math.max(20, Math.ceil(wordCount / 1000))
    }
    return CREDIT_CONFIG.DEFAULT_NOVEL_CREDITS
  }
}

export const formatCreditPrice = (credits: number): string => {
  const usdValue = creditsToUSD(credits)
  return `${formatCredits(credits)} ($${usdValue.toFixed(2)})`
}

// Validation helpers
export const isValidCreditPrice = (credits: number): boolean => {
  return credits > 0 && credits <= 1000 && Number.isInteger(credits)
}

export const validateCreditPurchase = (userBalance: number, requiredCredits: number): {
  canPurchase: boolean
  shortfall?: number
} => {
  if (userBalance >= requiredCredits) {
    return { canPurchase: true }
  }
  
  return {
    canPurchase: false,
    shortfall: requiredCredits - userBalance
  }
}
