import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'
import { novelsApi } from '@/store/api/novelsApi'
import { chaptersApi } from '@/store/api/chaptersApi'
import { libraryApi } from '@/store/api/libraryApi'
import { authorsApi } from '@/store/api/authorsApi'
import { subscriptionsApi } from '@/store/api/subscriptionsApi'
import { earningsApi } from '@/store/api/earningsApi'
import { payoutsApi } from '@/store/api/payoutsApi'
import { creditsApi } from '@/store/api/creditsApi'
import { authSlice } from '@/store/slices/authSlice'
import { uiSlice } from '@/store/slices/uiSlice'
import { creditSlice } from '@/store/slices/creditSlice'

export const store = configureStore({
  reducer: {
    // API slices
    [novelsApi.reducerPath]: novelsApi.reducer,
    [chaptersApi.reducerPath]: chaptersApi.reducer,
    [libraryApi.reducerPath]: libraryApi.reducer,
    [authorsApi.reducerPath]: authorsApi.reducer,
    [subscriptionsApi.reducerPath]: subscriptionsApi.reducer,
    [earningsApi.reducerPath]: earningsApi.reducer,
    [payoutsApi.reducerPath]: payoutsApi.reducer,
    [creditsApi.reducerPath]: creditsApi.reducer,

    // Regular slices
    auth: authSlice.reducer,
    ui: uiSlice.reducer,
    credit: creditSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      novelsApi.middleware,
      chaptersApi.middleware,
      libraryApi.middleware,
      authorsApi.middleware,
      subscriptionsApi.middleware,
      earningsApi.middleware,
      payoutsApi.middleware,
      creditsApi.middleware
    ),
})

setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch