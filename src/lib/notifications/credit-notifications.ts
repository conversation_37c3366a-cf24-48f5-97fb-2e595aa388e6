import { prisma } from '@/lib/db'
import { sendEmail } from '@/lib/email'
import {
  creditPurchaseEmailTemplate,
  lowBalanceEmailTemplate,
  contentUnlockEmailTemplate,
  writerEarningsEmailTemplate,
  type CreditPurchaseEmailData,
  type LowBalanceEmailData,
  type ContentUnlockEmailData,
  type WriterEarningsEmailData,
} from '@/lib/email/credit-templates'

export class CreditNotificationService {
  /**
   * Send credit purchase confirmation email
   */
  static async sendCreditPurchaseNotification(purchaseId: string) {
    try {
      const purchase = await prisma.creditPurchase.findUnique({
        where: { id: purchaseId },
        include: {
          user: true,
          package: true,
        },
      })

      if (!purchase || !purchase.user.email) {
        console.log('Purchase not found or user has no email:', purchaseId)
        return
      }

      // Check if user wants email notifications
      const userPreferences = await prisma.user.findUnique({
        where: { id: purchase.userId },
        select: { emailNotifications: true },
      })

      if (!userPreferences?.emailNotifications) {
        console.log('User has disabled email notifications:', purchase.userId)
        return
      }

      const emailData: CreditPurchaseEmailData = {
        userName: purchase.user.name || purchase.user.email,
        packageName: purchase.package.name,
        credits: purchase.credits,
        bonusCredits: purchase.bonusCredits,
        totalCredits: purchase.totalCredits,
        amount: purchase.amount.toNumber(),
        transactionId: purchase.id,
        purchaseDate: purchase.createdAt.toISOString(),
      }

      const { subject, html, text } = creditPurchaseEmailTemplate(emailData)

      await sendEmail({
        to: purchase.user.email,
        subject,
        html,
        text,
      })

      console.log('Credit purchase notification sent:', purchase.user.email)
    } catch (error) {
      console.error('Error sending credit purchase notification:', error)
    }
  }

  /**
   * Send low balance warning email
   */
  static async sendLowBalanceNotification(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          email: true,
          name: true,
          creditBalance: true,
          emailNotifications: true,
        },
      })

      if (!user || !user.email || !user.emailNotifications) {
        return
      }

      // Get recommended packages
      const packages = await prisma.creditPackage.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: 'asc' },
        take: 3,
      })

      const emailData: LowBalanceEmailData = {
        userName: user.name || user.email,
        currentBalance: user.creditBalance,
        threshold: 10, // Default threshold
        recommendedPackages: packages.map(pkg => ({
          name: pkg.name,
          credits: pkg.credits + pkg.bonusCredits,
          price: pkg.price.toNumber(),
        })),
      }

      const { subject, html, text } = lowBalanceEmailTemplate(emailData)

      await sendEmail({
        to: user.email,
        subject,
        html,
        text,
      })

      console.log('Low balance notification sent:', user.email)
    } catch (error) {
      console.error('Error sending low balance notification:', error)
    }
  }

  /**
   * Send content unlock confirmation email
   */
  static async sendContentUnlockNotification(contentPurchaseId: string) {
    try {
      const contentPurchase = await prisma.contentPurchase.findUnique({
        where: { id: contentPurchaseId },
        include: {
          user: true,
        },
      })

      if (!contentPurchase || !contentPurchase.user.email || !contentPurchase.user.emailNotifications) {
        return
      }

      // Get content details
      let contentTitle = 'Unknown Content'
      let authorName = 'Unknown Author'

      if (contentPurchase.contentType === 'NOVEL') {
        const novel = await prisma.novel.findUnique({
          where: { id: contentPurchase.contentId },
          include: { author: true },
        })
        if (novel) {
          contentTitle = novel.title
          authorName = novel.author.name || novel.author.email
        }
      } else if (contentPurchase.contentType === 'CHAPTER') {
        const chapter = await prisma.chapter.findUnique({
          where: { id: contentPurchase.contentId },
          include: {
            novel: {
              include: { author: true },
            },
          },
        })
        if (chapter) {
          contentTitle = chapter.title
          authorName = chapter.novel.author.name || chapter.novel.author.email
        }
      }

      const emailData: ContentUnlockEmailData = {
        userName: contentPurchase.user.name || contentPurchase.user.email,
        contentTitle,
        contentType: contentPurchase.contentType,
        creditsSpent: contentPurchase.creditsSpent,
        remainingBalance: contentPurchase.user.creditBalance,
        authorName,
      }

      const { subject, html, text } = contentUnlockEmailTemplate(emailData)

      await sendEmail({
        to: contentPurchase.user.email,
        subject,
        html,
        text,
      })

      console.log('Content unlock notification sent:', contentPurchase.user.email)
    } catch (error) {
      console.error('Error sending content unlock notification:', error)
    }
  }

  /**
   * Send writer earnings notification
   */
  static async sendWriterEarningsNotification(earningId: string) {
    try {
      const earning = await prisma.earning.findUnique({
        where: { id: earningId },
        include: {
          user: true,
        },
      })

      if (!earning || earning.type !== 'CREDIT_PURCHASE' || !earning.user.email || !earning.user.emailNotifications) {
        return
      }

      // Get content details
      let contentTitle = 'Unknown Content'
      let buyerName: string | undefined

      if (earning.sourceType === 'NOVEL') {
        const novel = await prisma.novel.findUnique({
          where: { id: earning.sourceId! },
        })
        contentTitle = novel?.title || 'Unknown Novel'
      } else if (earning.sourceType === 'CHAPTER') {
        const chapter = await prisma.chapter.findUnique({
          where: { id: earning.sourceId! },
        })
        contentTitle = chapter?.title || 'Unknown Chapter'
      }

      // Get buyer name if available
      if (earning.contentPurchaseId) {
        const contentPurchase = await prisma.contentPurchase.findUnique({
          where: { id: earning.contentPurchaseId },
          include: { user: true },
        })
        buyerName = contentPurchase?.user.name || undefined
      }

      // Get total earnings and unpaid balance
      const [totalEarnings, unpaidBalance] = await Promise.all([
        prisma.earning.aggregate({
          where: { userId: earning.userId },
          _sum: { authorEarning: true },
        }),
        prisma.earning.aggregate({
          where: { userId: earning.userId, isPaidOut: false },
          _sum: { authorEarning: true },
        }),
      ])

      const emailData: WriterEarningsEmailData = {
        authorName: earning.user.name || earning.user.email,
        contentTitle,
        contentType: earning.sourceType as 'NOVEL' | 'CHAPTER',
        creditsEarned: Math.round(earning.amount.toNumber() / 0.10), // Convert back to credits
        dollarAmount: earning.authorEarning.toNumber(),
        buyerName,
        totalEarnings: totalEarnings._sum.authorEarning?.toNumber() || 0,
        unpaidBalance: unpaidBalance._sum.authorEarning?.toNumber() || 0,
      }

      const { subject, html, text } = writerEarningsEmailTemplate(emailData)

      await sendEmail({
        to: earning.user.email,
        subject,
        html,
        text,
      })

      console.log('Writer earnings notification sent:', earning.user.email)
    } catch (error) {
      console.error('Error sending writer earnings notification:', error)
    }
  }

  /**
   * Check and send low balance warnings for all users
   */
  static async checkAndSendLowBalanceWarnings() {
    try {
      const threshold = 10 // Default low balance threshold

      // Find users with low balance who haven't been warned recently
      const usersWithLowBalance = await prisma.user.findMany({
        where: {
          creditBalance: {
            lte: threshold,
          },
          emailNotifications: true,
          email: {
            not: null,
          },
          // Only send warning once per day
          lastLowBalanceWarning: {
            lt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          },
        },
      })

      for (const user of usersWithLowBalance) {
        await this.sendLowBalanceNotification(user.id)
        
        // Update last warning timestamp
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLowBalanceWarning: new Date() },
        })
      }

      console.log(`Sent low balance warnings to ${usersWithLowBalance.length} users`)
    } catch (error) {
      console.error('Error checking low balance warnings:', error)
    }
  }

  /**
   * Send batch notifications for multiple events
   */
  static async sendBatchNotifications(events: Array<{
    type: 'credit_purchase' | 'low_balance' | 'content_unlock' | 'writer_earnings'
    id: string
  }>) {
    const promises = events.map(event => {
      switch (event.type) {
        case 'credit_purchase':
          return this.sendCreditPurchaseNotification(event.id)
        case 'low_balance':
          return this.sendLowBalanceNotification(event.id)
        case 'content_unlock':
          return this.sendContentUnlockNotification(event.id)
        case 'writer_earnings':
          return this.sendWriterEarningsNotification(event.id)
        default:
          return Promise.resolve()
      }
    })

    await Promise.allSettled(promises)
  }
}
