import { Storage } from '@google-cloud/storage'
import { v4 as uuidv4 } from 'uuid'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

interface GCSConfig {
  projectId: string
  bucketName: string
  keyFilename?: string
  credentials?: object
}

// Function to get gcloud credentials
async function getGcloudCredentials(): Promise<{ projectId: string; accessToken: string }> {
  try {
    // Get project ID
    const { stdout: projectId } = await execAsync('gcloud config get-value project')
    
    // Get access token
    const { stdout: accessToken } = await execAsync('gcloud auth print-access-token')
    
    return {
      projectId: projectId.trim(),
      accessToken: accessToken.trim()
    }
  } catch (error) {
    console.error('Failed to get gcloud credentials:', error)
    throw new Error('Please run: gcloud auth login && gcloud auth application-default login')
  }
}

class GCSManager {
  private storage: Storage
  private bucketName: string
  public bucket: any

  constructor(config: GCSConfig) {
    this.storage = new Storage({
      projectId: config.projectId,
      keyFilename: config.keyFilename,
      credentials: config.credentials,
    })
    this.bucketName = config.bucketName
    this.bucket = this.storage.bucket(this.bucketName)
  }

  async uploadFile(
    file: Buffer | Uint8Array,
    filename: string,
    contentType: string,
    folder = 'covers'
  ): Promise<{ filename: string; url: string }> {
    const fileExtension = filename.split('.').pop()
    const uniqueFilename = `${folder}/${uuidv4()}.${fileExtension}`
    
    const gcsFile = this.bucket.file(uniqueFilename)
    
    await gcsFile.save(file, {
      metadata: {
        contentType,
        cacheControl: 'public, max-age=3600',
      },
    })

    return {
      filename: uniqueFilename,
      url: `gs://${this.bucketName}/${uniqueFilename}`,
    }
  }

  async deleteFile(filename: string): Promise<boolean> {
    try {
      const file = this.bucket.file(filename)
      await file.delete()
      return true
    } catch (error) {
      console.error('Error deleting file:', error)
      return false
    }
  }

  async getSignedUrl(filename: string, expiresIn = 3600): Promise<string> {
    const file = this.bucket.file(filename)
    
    const [url] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + expiresIn * 1000,
    })
    
    return url
  }

  async fileExists(filename: string): Promise<boolean> {
    try {
      const file = this.bucket.file(filename)
      const [exists] = await file.exists()
      return exists
    } catch (error) {
      return false
    }
  }

  async getPublicUrl(filename: string): Promise<string> {
    return `https://storage.googleapis.com/${this.bucketName}/${filename}`
  }
}

// Initialize GCS manager with gcloud credentials
async function initializeGCSManager(): Promise<GCSManager> {
  let gcsConfig: GCSConfig

  // Try to use environment variables first
  if (process.env.GOOGLE_CLOUD_PROJECT_ID && process.env.GOOGLE_CLOUD_BUCKET_NAME) {
    gcsConfig = {
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
      bucketName: process.env.GOOGLE_CLOUD_BUCKET_NAME,
      keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE,
      credentials: process.env.GOOGLE_CLOUD_CREDENTIALS ? 
        JSON.parse(process.env.GOOGLE_CLOUD_CREDENTIALS) : undefined,
    }
  } else {
    // Fall back to gcloud credentials
    const { projectId } = await getGcloudCredentials()
    gcsConfig = {
      projectId,
      bucketName: process.env.GOOGLE_CLOUD_BUCKET_NAME || `${projectId}-media`,
    }
  }

  // If no credentials provided, use Application Default Credentials (ADC)
  if (!gcsConfig.keyFilename && !gcsConfig.credentials) {
    delete gcsConfig.keyFilename
    delete gcsConfig.credentials
  }

  return new GCSManager(gcsConfig)
}

// Create a singleton instance
let gcsManagerInstance: GCSManager | null = null

export async function getGCSManager(): Promise<GCSManager> {
  if (!gcsManagerInstance) {
    gcsManagerInstance = await initializeGCSManager()
  }
  return gcsManagerInstance
}

// Utility functions for image handling
export async function uploadCoverImage(
  file: Buffer | Uint8Array,
  filename: string,
  contentType: string
): Promise<{ filename: string; url: string }> {
  const manager = await getGCSManager()
  return await manager.uploadFile(file, filename, contentType, 'covers')
}

export async function deleteCoverImage(filename: string): Promise<boolean> {
  const manager = await getGCSManager()
  return await manager.deleteFile(filename)
}

export async function getCoverImageUrl(filename: string): Promise<string> {
  // Return a secure API endpoint instead of direct GCS URL
  return `/api/images/cover/${encodeURIComponent(filename)}`
}

export async function getChapterImageUrl(filename: string): Promise<string> {
  // Return a secure API endpoint for chapter images
  return `/api/images/chapter/${encodeURIComponent(filename)}`
}

// Chapter image utility functions
export async function uploadChapterImage(
  file: Buffer | Uint8Array,
  filename: string,
  contentType: string
): Promise<{ filename: string; url: string }> {
  const manager = await getGCSManager()
  return await manager.uploadFile(file, filename, contentType, 'chapters')
}

export async function deleteChapterImage(filename: string): Promise<boolean> {
  const manager = await getGCSManager()
  return await manager.deleteFile(filename)
}

export async function getSignedCoverImageUrl(filename: string): Promise<string> {
  const manager = await getGCSManager()
  return await manager.getSignedUrl(filename)
}

export async function getSignedChapterImageUrl(filename: string): Promise<string> {
  const manager = await getGCSManager()
  return await manager.getSignedUrl(filename)
}

export { GCSManager }