import { useState, useEffect } from 'react'
import { useMediaQuery } from '@/hooks/use-media-query'

/**
 * Hook for managing mobile-specific credit system behavior
 */
export function useMobileCredits() {
  const isMobile = useMediaQuery('(max-width: 768px)')
  const isTablet = useMediaQuery('(max-width: 1024px)')
  const isTouchDevice = useMediaQuery('(hover: none) and (pointer: coarse)')
  
  const [isBottomSheetPreferred, setIsBottomSheetPreferred] = useState(false)

  useEffect(() => {
    // Prefer bottom sheets on mobile and touch devices
    setIsBottomSheetPreferred(isMobile || isTouchDevice)
  }, [isMobile, isTouchDevice])

  return {
    isMobile,
    isTablet,
    isTouchDevice,
    isBottomSheetPreferred,
    // Responsive grid columns for credit packages
    getPackageGridCols: () => {
      if (isMobile) return 1
      if (isTablet) return 2
      return 4
    },
    // Responsive card sizing
    getCardSize: () => {
      if (isMobile) return 'compact'
      return 'default'
    },
    // Touch-friendly button sizing
    getButtonSize: () => {
      if (isTouchDevice) return 'lg'
      return 'default'
    },
    // Optimal modal/sheet type
    getModalType: () => {
      return isBottomSheetPreferred ? 'sheet' : 'modal'
    }
  }
}

/**
 * Hook for responsive credit display formatting
 */
export function useCreditFormatting() {
  const { isMobile } = useMobileCredits()

  const formatCreditsResponsive = (credits: number) => {
    if (isMobile && credits >= 1000) {
      // Use abbreviated format on mobile for large numbers
      if (credits >= 1000000) {
        return `${(credits / 1000000).toFixed(1)}M credits`
      }
      if (credits >= 1000) {
        return `${(credits / 1000).toFixed(1)}K credits`
      }
    }
    return `${credits.toLocaleString()} credits`
  }

  const formatPriceResponsive = (price: number) => {
    if (isMobile) {
      return `$${price.toFixed(2)}`
    }
    return `$${price.toFixed(2)} USD`
  }

  return {
    formatCreditsResponsive,
    formatPriceResponsive,
    shouldShowFullDescription: !isMobile
  }
}

/**
 * Hook for managing mobile credit purchase flow
 */
export function useMobileCreditPurchase() {
  const { isMobile, isBottomSheetPreferred } = useMobileCredits()
  const [purchaseStep, setPurchaseStep] = useState<'select' | 'payment' | 'processing' | 'success'>('select')

  const resetPurchaseFlow = () => {
    setPurchaseStep('select')
  }

  const nextStep = () => {
    setPurchaseStep(current => {
      switch (current) {
        case 'select': return 'payment'
        case 'payment': return 'processing'
        case 'processing': return 'success'
        default: return current
      }
    })
  }

  return {
    purchaseStep,
    setPurchaseStep,
    resetPurchaseFlow,
    nextStep,
    shouldUseBottomSheet: isBottomSheetPreferred,
    shouldShowStepIndicator: isMobile
  }
}

/**
 * Hook for mobile-optimized credit notifications
 */
export function useMobileCreditNotifications() {
  const { isMobile } = useMobileCredits()

  const getNotificationConfig = () => ({
    position: isMobile ? 'bottom' : 'top-right',
    duration: isMobile ? 3000 : 5000, // Shorter on mobile
    maxWidth: isMobile ? '90vw' : '400px',
    showIcon: !isMobile, // Save space on mobile
  })

  const formatNotificationMessage = (message: string) => {
    if (isMobile && message.length > 50) {
      return message.substring(0, 47) + '...'
    }
    return message
  }

  return {
    getNotificationConfig,
    formatNotificationMessage,
    shouldUseCompactNotifications: isMobile
  }
}

/**
 * Hook for mobile credit analytics display
 */
export function useMobileCreditAnalytics() {
  const { isMobile, isTablet } = useMobileCredits()

  const getChartConfig = () => ({
    height: isMobile ? 200 : 300,
    showLegend: !isMobile,
    showTooltip: true,
    responsive: true,
    maintainAspectRatio: false,
  })

  const getMetricCardLayout = () => ({
    columns: isMobile ? 2 : isTablet ? 3 : 4,
    showDescription: !isMobile,
    compactNumbers: isMobile,
  })

  return {
    getChartConfig,
    getMetricCardLayout,
    shouldUseHorizontalLayout: isMobile,
    shouldShowExpandedView: !isMobile
  }
}
