import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@prisma/client"
import { z } from "zod"

const analyticsQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y', 'all']).default('30d'),
  granularity: z.enum(['day', 'week', 'month']).default('day'),
})

// GET /api/analytics/credits - Get comprehensive credit system analytics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = analyticsQuerySchema.parse({
      period: searchParams.get('period') || '30d',
      granularity: searchParams.get('granularity') || 'day',
    })

    // Calculate date range
    const now = new Date()
    let startDate: Date
    
    switch (query.period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date('2020-01-01') // All time
    }

    // Get overall metrics
    const [
      totalUsers,
      activeUsers,
      totalCreditsSold,
      totalRevenue,
      totalTransactions,
      averageTransactionValue,
      topPackages,
      userGrowth,
      revenueGrowth,
      conversionMetrics
    ] = await Promise.all([
      // Total users with credits
      prisma.user.count({
        where: { creditBalance: { gt: 0 } }
      }),

      // Active users (made a transaction in period)
      prisma.user.count({
        where: {
          creditTransactions: {
            some: {
              createdAt: { gte: startDate }
            }
          }
        }
      }),

      // Total credits sold
      prisma.creditTransaction.aggregate({
        where: {
          type: 'PURCHASE',
          status: 'COMPLETED',
          createdAt: { gte: startDate }
        },
        _sum: { amount: true }
      }),

      // Total revenue
      prisma.creditPurchase.aggregate({
        where: {
          status: 'COMPLETED',
          createdAt: { gte: startDate }
        },
        _sum: { amount: true }
      }),

      // Total transactions
      prisma.creditTransaction.count({
        where: {
          createdAt: { gte: startDate }
        }
      }),

      // Average transaction value
      prisma.creditPurchase.aggregate({
        where: {
          status: 'COMPLETED',
          createdAt: { gte: startDate }
        },
        _avg: { amount: true }
      }),

      // Top selling packages
      prisma.creditPurchase.groupBy({
        by: ['packageId'],
        where: {
          status: 'COMPLETED',
          createdAt: { gte: startDate }
        },
        _count: { packageId: true },
        _sum: { amount: true, totalCredits: true },
        orderBy: { _count: { packageId: 'desc' } },
        take: 5
      }),

      // User growth over time
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC(${query.granularity}, created_at) as period,
          COUNT(*) as new_users
        FROM users 
        WHERE created_at >= ${startDate}
        GROUP BY DATE_TRUNC(${query.granularity}, created_at)
        ORDER BY period ASC
      `,

      // Revenue growth over time
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC(${query.granularity}, created_at) as period,
          SUM(amount) as revenue,
          COUNT(*) as transactions
        FROM credit_purchases 
        WHERE status = 'COMPLETED' AND created_at >= ${startDate}
        GROUP BY DATE_TRUNC(${query.granularity}, created_at)
        ORDER BY period ASC
      `,

      // Conversion metrics
      getConversionMetrics(startDate)
    ])

    // Get package details for top packages
    const packageIds = topPackages.map(p => p.packageId)
    const packageDetails = await prisma.creditPackage.findMany({
      where: { id: { in: packageIds } },
      select: { id: true, name: true, credits: true, price: true }
    })

    // Combine package data
    const topPackagesWithDetails = topPackages.map(pkg => {
      const details = packageDetails.find(p => p.id === pkg.packageId)
      return {
        ...pkg,
        package: details
      }
    })

    // Get content analytics
    const contentAnalytics = await getContentAnalytics(startDate)

    // Get user behavior analytics
    const userBehavior = await getUserBehaviorAnalytics(startDate)

    return NextResponse.json({
      period: query.period,
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString()
      },
      overview: {
        totalUsers,
        activeUsers,
        totalCreditsSold: totalCreditsSold._sum.amount || 0,
        totalRevenue: totalRevenue._sum.amount || 0,
        totalTransactions,
        averageTransactionValue: averageTransactionValue._avg.amount || 0,
        conversionRate: conversionMetrics.conversionRate,
      },
      trends: {
        userGrowth,
        revenueGrowth,
      },
      packages: {
        topSelling: topPackagesWithDetails,
      },
      content: contentAnalytics,
      userBehavior,
      conversionMetrics,
    })
  } catch (error) {
    console.error("Error fetching credit analytics:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

async function getConversionMetrics(startDate: Date) {
  const [totalVisitors, creditPurchasers, contentPurchasers] = await Promise.all([
    // Approximate total visitors (users who signed up)
    prisma.user.count({
      where: { createdAt: { gte: startDate } }
    }),

    // Users who purchased credits
    prisma.user.count({
      where: {
        creditPurchases: {
          some: {
            status: 'COMPLETED',
            createdAt: { gte: startDate }
          }
        }
      }
    }),

    // Users who purchased content with credits
    prisma.user.count({
      where: {
        contentPurchases: {
          some: {
            createdAt: { gte: startDate }
          }
        }
      }
    })
  ])

  return {
    totalVisitors,
    creditPurchasers,
    contentPurchasers,
    conversionRate: totalVisitors > 0 ? (creditPurchasers / totalVisitors) * 100 : 0,
    contentConversionRate: creditPurchasers > 0 ? (contentPurchasers / creditPurchasers) * 100 : 0,
  }
}

async function getContentAnalytics(startDate: Date) {
  const [topContent, contentRevenue, contentTypes] = await Promise.all([
    // Top purchased content
    prisma.contentPurchase.groupBy({
      by: ['contentType', 'contentId'],
      where: { createdAt: { gte: startDate } },
      _count: { contentId: true },
      _sum: { creditsSpent: true },
      orderBy: { _count: { contentId: 'desc' } },
      take: 10
    }),

    // Revenue by content type
    prisma.contentPurchase.groupBy({
      by: ['contentType'],
      where: { createdAt: { gte: startDate } },
      _sum: { creditsSpent: true, priceAtTime: true },
      _count: { contentType: true }
    }),

    // Content type distribution
    prisma.contentPurchase.groupBy({
      by: ['contentType'],
      where: { createdAt: { gte: startDate } },
      _count: { contentType: true }
    })
  ])

  return {
    topContent,
    revenueByType: contentRevenue,
    typeDistribution: contentTypes,
  }
}

async function getUserBehaviorAnalytics(startDate: Date) {
  const [balanceDistribution, spendingPatterns, retentionMetrics] = await Promise.all([
    // Credit balance distribution
    prisma.$queryRaw`
      SELECT 
        CASE 
          WHEN credit_balance = 0 THEN '0'
          WHEN credit_balance BETWEEN 1 AND 10 THEN '1-10'
          WHEN credit_balance BETWEEN 11 AND 50 THEN '11-50'
          WHEN credit_balance BETWEEN 51 AND 100 THEN '51-100'
          WHEN credit_balance BETWEEN 101 AND 500 THEN '101-500'
          ELSE '500+'
        END as balance_range,
        COUNT(*) as user_count
      FROM users
      GROUP BY balance_range
      ORDER BY balance_range
    `,

    // Spending patterns
    prisma.$queryRaw`
      SELECT 
        AVG(credits_spent) as avg_credits_per_purchase,
        MIN(credits_spent) as min_credits,
        MAX(credits_spent) as max_credits,
        COUNT(*) as total_purchases
      FROM content_purchases
      WHERE created_at >= ${startDate}
    `,

    // User retention (users who made multiple purchases)
    prisma.$queryRaw`
      SELECT 
        COUNT(DISTINCT user_id) as total_users,
        COUNT(DISTINCT CASE WHEN purchase_count > 1 THEN user_id END) as repeat_users
      FROM (
        SELECT user_id, COUNT(*) as purchase_count
        FROM content_purchases
        WHERE created_at >= ${startDate}
        GROUP BY user_id
      ) user_purchases
    `
  ])

  return {
    balanceDistribution,
    spendingPatterns: spendingPatterns[0],
    retentionMetrics: retentionMetrics[0],
  }
}
