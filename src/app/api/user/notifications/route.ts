import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { z } from "zod"

const updateNotificationPreferencesSchema = z.object({
  emailNotifications: z.boolean().optional(),
})

// GET /api/user/notifications - Get user notification preferences
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        emailNotifications: true,
        lastLowBalanceWarning: true,
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    return NextResponse.json({
      preferences: {
        emailNotifications: user.emailNotifications,
        lastLowBalanceWarning: user.lastLowBalanceWarning,
      }
    })
  } catch (error) {
    console.error("Error fetching notification preferences:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/user/notifications - Update user notification preferences
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const preferences = updateNotificationPreferencesSchema.parse(body)

    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: preferences,
      select: {
        emailNotifications: true,
        lastLowBalanceWarning: true,
      }
    })

    return NextResponse.json({
      message: "Notification preferences updated successfully",
      preferences: {
        emailNotifications: updatedUser.emailNotifications,
        lastLowBalanceWarning: updatedUser.lastLowBalanceWarning,
      }
    })
  } catch (error) {
    console.error("Error updating notification preferences:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
