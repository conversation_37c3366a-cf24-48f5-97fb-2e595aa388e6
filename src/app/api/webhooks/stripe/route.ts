import { NextRequest, NextResponse } from "next/server"
import { headers } from "next/headers"
import { prisma } from "@/lib/db"
import { verifyStripeWebhook, calculateRevenueSplit } from "@/lib/stripe"
import { SubscriptionStatus, PaymentStatus, EarningType, CreditTransactionType, CreditTransactionStatus } from "@prisma/client"
import { CreditNotificationService } from "@/lib/notifications/credit-notifications"
import <PERSON><PERSON> from "stripe"

// Disable body parsing for webhook
export const runtime = 'nodejs'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = headers().get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { error: "Missing stripe signature" },
        { status: 400 }
      )
    }

    // Verify webhook signature
    const event = verifyStripeWebhook(body, signature)

    console.log(`Processing Stripe webhook: ${event.type}`)

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionUpdate(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        break

      case 'payment_intent.succeeded':
        await handleCreditPurchaseSucceeded(event.data.object as Stripe.PaymentIntent)
        break

      case 'payment_intent.payment_failed':
        await handleCreditPurchaseFailed(event.data.object as Stripe.PaymentIntent)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error("Webhook error:", error)
    return NextResponse.json(
      { error: "Webhook handler failed" },
      { status: 400 }
    )
  }
}

async function handleSubscriptionUpdate(stripeSubscription: Stripe.Subscription) {
  try {
    const subscription = await prisma.subscription.findFirst({
      where: {
        stripeSubscriptionId: stripeSubscription.id,
      },
    })

    if (!subscription) {
      console.error(`Subscription not found: ${stripeSubscription.id}`)
      return
    }

    // Map Stripe status to our enum
    let status: SubscriptionStatus
    switch (stripeSubscription.status) {
      case 'active':
        status = SubscriptionStatus.ACTIVE
        break
      case 'canceled':
        status = SubscriptionStatus.CANCELED
        break
      case 'past_due':
        status = SubscriptionStatus.PAST_DUE
        break
      case 'unpaid':
        status = SubscriptionStatus.UNPAID
        break
      case 'incomplete':
        status = SubscriptionStatus.INCOMPLETE
        break
      case 'incomplete_expired':
        status = SubscriptionStatus.INCOMPLETE_EXPIRED
        break
      case 'trialing':
        status = SubscriptionStatus.TRIALING
        break
      default:
        status = SubscriptionStatus.ACTIVE
    }

    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
        trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,
        trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
      },
    })

    console.log(`Updated subscription ${subscription.id} to status ${status}`)
  } catch (error) {
    console.error("Error handling subscription update:", error)
  }
}

async function handleSubscriptionDeleted(stripeSubscription: Stripe.Subscription) {
  try {
    const subscription = await prisma.subscription.findFirst({
      where: {
        stripeSubscriptionId: stripeSubscription.id,
      },
    })

    if (!subscription) {
      console.error(`Subscription not found: ${stripeSubscription.id}`)
      return
    }

    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: SubscriptionStatus.CANCELED,
        canceledAt: new Date(),
      },
    })

    console.log(`Canceled subscription ${subscription.id}`)
  } catch (error) {
    console.error("Error handling subscription deletion:", error)
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    if (!invoice.subscription) return

    const subscription = await prisma.subscription.findFirst({
      where: {
        stripeSubscriptionId: invoice.subscription as string,
      },
      include: {
        user: true,
      },
    })

    if (!subscription) {
      console.error(`Subscription not found: ${invoice.subscription}`)
      return
    }

    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        userId: subscription.userId,
        subscriptionId: subscription.id,
        stripePaymentId: invoice.payment_intent as string,
        amount: invoice.amount_paid / 100, // Convert from cents
        currency: invoice.currency,
        status: PaymentStatus.COMPLETED,
        description: `Subscription payment for ${subscription.tier}`,
      },
    })

    console.log(`Created payment record ${payment.id} for subscription ${subscription.id}`)
  } catch (error) {
    console.error("Error handling payment succeeded:", error)
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  try {
    if (!invoice.subscription) return

    const subscription = await prisma.subscription.findFirst({
      where: {
        stripeSubscriptionId: invoice.subscription as string,
      },
    })

    if (!subscription) {
      console.error(`Subscription not found: ${invoice.subscription}`)
      return
    }

    // Create failed payment record
    await prisma.payment.create({
      data: {
        userId: subscription.userId,
        subscriptionId: subscription.id,
        stripePaymentId: invoice.payment_intent as string,
        amount: invoice.amount_due / 100, // Convert from cents
        currency: invoice.currency,
        status: PaymentStatus.FAILED,
        description: `Failed subscription payment for ${subscription.tier}`,
        failureReason: 'Payment failed',
      },
    })

    console.log(`Created failed payment record for subscription ${subscription.id}`)
  } catch (error) {
    console.error("Error handling payment failed:", error)
  }
}

// Handle successful credit purchase
async function handleCreditPurchaseSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    if (paymentIntent.metadata?.type !== 'credit_purchase') {
      return // Not a credit purchase
    }

    const { userId, packageId, credits, bonusCredits } = paymentIntent.metadata

    // Find the credit purchase record
    const creditPurchase = await prisma.creditPurchase.findFirst({
      where: {
        stripePaymentId: paymentIntent.id,
        status: PaymentStatus.PENDING,
      },
      include: {
        user: true,
        package: true,
      }
    })

    if (!creditPurchase) {
      console.log(`Credit purchase not found for payment intent ${paymentIntent.id}`)
      return
    }

    const currentBalance = creditPurchase.user.creditBalance
    const totalCredits = parseInt(credits) + parseInt(bonusCredits)
    const newBalance = currentBalance + totalCredits

    // Update purchase status and user balance in a transaction
    await prisma.$transaction([
      // Update credit purchase status
      prisma.creditPurchase.update({
        where: { id: creditPurchase.id },
        data: { status: PaymentStatus.COMPLETED }
      }),

      // Update user credit balance
      prisma.user.update({
        where: { id: creditPurchase.userId },
        data: { creditBalance: newBalance }
      }),

      // Create credit transaction record
      prisma.creditTransaction.create({
        data: {
          userId: creditPurchase.userId,
          type: CreditTransactionType.PURCHASE,
          status: CreditTransactionStatus.COMPLETED,
          amount: totalCredits,
          description: `Purchased ${credits} credits${parseInt(bonusCredits) > 0 ? ` + ${bonusCredits} bonus` : ''} (${creditPurchase.package.name})`,
          sourceType: 'purchase',
          sourceId: creditPurchase.id,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          purchaseId: creditPurchase.id,
        }
      })
    ])

    // Send purchase confirmation email
    CreditNotificationService.sendCreditPurchaseNotification(creditPurchase.id).catch(error => {
      console.error('Error sending credit purchase notification:', error)
    })

    console.log(`Processed successful credit purchase: ${totalCredits} credits for user ${userId}`)
  } catch (error) {
    console.error("Error handling credit purchase succeeded:", error)
  }
}

// Handle failed credit purchase
async function handleCreditPurchaseFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    if (paymentIntent.metadata?.type !== 'credit_purchase') {
      return // Not a credit purchase
    }

    // Update credit purchase status to failed
    await prisma.creditPurchase.updateMany({
      where: {
        stripePaymentId: paymentIntent.id,
        status: PaymentStatus.PENDING,
      },
      data: {
        status: PaymentStatus.FAILED,
        failureReason: 'Payment failed',
      }
    })

    console.log(`Marked credit purchase as failed for payment intent ${paymentIntent.id}`)
  } catch (error) {
    console.error("Error handling credit purchase failed:", error)
  }
}
