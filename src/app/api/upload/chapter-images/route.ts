import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { uploadChapterImage, getChapterImageUrl, deleteChapterImage } from "@/lib/gcs"

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json(
        { error: "Unauthorized. Only authors can upload chapter images." },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get("file") as File
    const chapterId = formData.get("chapterId") as string

    // Validate inputs
    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      )
    }

    if (!chapterId) {
      return NextResponse.json(
        { error: "Chapter ID is required" },
        { status: 400 }
      )
    }

    // Verify chapter ownership
    const chapter = await prisma.chapter.findFirst({
      where: {
        id: chapterId,
      },
      include: {
        novel: {
          select: {
            authorId: true,
          },
        },
      },
    })

    if (!chapter || chapter.novel.authorId !== session.user.id) {
      return NextResponse.json(
        { error: "Chapter not found or you don't have permission to edit it" },
        { status: 404 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Only JPG, PNG, and WebP files are allowed." },
        { status: 400 }
      )
    }

    // Validate file size (max 10MB for chapter images)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size too large. Maximum size is 10MB." },
        { status: 400 }
      )
    }

    // Generate unique filename for chapter image
    const fileExt = file.name.split('.').pop()?.toLowerCase()
    const timestamp = Date.now()
    const filename = `chapter-${chapterId}-${timestamp}.${fileExt}`

    // Upload file to Google Cloud Storage
    const fileBuffer = Buffer.from(await file.arrayBuffer())
    const uploadResult = await uploadChapterImage(fileBuffer, filename, file.type)
    const publicUrl = await getChapterImageUrl(uploadResult.filename)

    return NextResponse.json({
      success: true,
      data: {
        filename: uploadResult.filename,
        url: publicUrl,
        originalName: file.name,
      },
      message: "Chapter image uploaded successfully",
    })
  } catch (error) {
    console.error("Error uploading chapter image:", error)
    return NextResponse.json(
      { error: "Failed to upload chapter image" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json(
        { error: "Unauthorized. Only authors can delete chapter images." },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const filename = searchParams.get("filename")
    const chapterId = searchParams.get("chapterId")

    if (!filename || !chapterId) {
      return NextResponse.json(
        { error: "Filename and chapter ID are required" },
        { status: 400 }
      )
    }

    // Verify chapter ownership
    const chapter = await prisma.chapter.findFirst({
      where: {
        id: chapterId,
      },
      include: {
        novel: {
          select: {
            authorId: true,
          },
        },
      },
    })

    if (!chapter || chapter.novel.authorId !== session.user.id) {
      return NextResponse.json(
        { error: "Chapter not found or you don't have permission to edit it" },
        { status: 404 }
      )
    }

    // Delete file from Google Cloud Storage
    await deleteChapterImage(filename)

    return NextResponse.json({
      success: true,
      message: "Chapter image deleted successfully",
    })
  } catch (error) {
    console.error("Error deleting chapter image:", error)
    return NextResponse.json(
      { error: "Failed to delete chapter image" },
      { status: 500 }
    )
  }
}
