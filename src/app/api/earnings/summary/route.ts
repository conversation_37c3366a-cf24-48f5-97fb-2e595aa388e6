import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole, EarningType } from "@prisma/client"

// GET /api/earnings/summary - Get comprehensive earnings summary for authors
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get all earnings for the user
    const allEarnings = await prisma.earning.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Get earnings for the specified period
    const periodEarnings = await prisma.earning.findMany({
      where: {
        userId: session.user.id,
        createdAt: {
          gte: startDate
        }
      }
    })

    // Calculate totals by earning type
    const totalsByType = allEarnings.reduce((acc, earning) => {
      const type = earning.type
      if (!acc[type]) {
        acc[type] = {
          count: 0,
          totalAmount: 0,
          authorEarning: 0,
          platformFee: 0
        }
      }
      acc[type].count += 1
      acc[type].totalAmount += Number(earning.amount)
      acc[type].authorEarning += Number(earning.authorEarning)
      acc[type].platformFee += Number(earning.platformFee)
      return acc
    }, {} as Record<string, any>)

    // Calculate period totals
    const periodTotalsByType = periodEarnings.reduce((acc, earning) => {
      const type = earning.type
      if (!acc[type]) {
        acc[type] = {
          count: 0,
          totalAmount: 0,
          authorEarning: 0,
          platformFee: 0
        }
      }
      acc[type].count += 1
      acc[type].totalAmount += Number(earning.amount)
      acc[type].authorEarning += Number(earning.authorEarning)
      acc[type].platformFee += Number(earning.platformFee)
      return acc
    }, {} as Record<string, any>)

    // Calculate overall totals
    const totalEarnings = allEarnings.reduce((sum, earning) => sum + Number(earning.authorEarning), 0)
    const totalPlatformFees = allEarnings.reduce((sum, earning) => sum + Number(earning.platformFee), 0)
    const totalRevenue = allEarnings.reduce((sum, earning) => sum + Number(earning.amount), 0)

    const periodTotalEarnings = periodEarnings.reduce((sum, earning) => sum + Number(earning.authorEarning), 0)
    const periodTotalRevenue = periodEarnings.reduce((sum, earning) => sum + Number(earning.amount), 0)

    // Get unpaid earnings
    const unpaidEarnings = await prisma.earning.findMany({
      where: {
        userId: session.user.id,
        isPaidOut: false
      }
    })

    const unpaidAmount = unpaidEarnings.reduce((sum, earning) => sum + Number(earning.authorEarning), 0)

    // Get recent payouts
    const recentPayouts = await prisma.payout.findMany({
      where: {
        userId: session.user.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    })

    // Calculate monthly earnings for trend
    const monthlyEarnings = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', created_at) as month,
        SUM(author_earning) as earnings,
        COUNT(*) as transactions,
        type
      FROM earnings 
      WHERE user_id = ${session.user.id}
        AND created_at >= NOW() - INTERVAL '12 months'
      GROUP BY DATE_TRUNC('month', created_at), type
      ORDER BY month DESC
    `

    // Credit-specific metrics
    const creditEarnings = allEarnings.filter(e => e.type === EarningType.CREDIT_PURCHASE)
    const creditMetrics = {
      totalCreditTransactions: creditEarnings.length,
      totalCreditRevenue: creditEarnings.reduce((sum, e) => sum + Number(e.amount), 0),
      totalCreditEarnings: creditEarnings.reduce((sum, e) => sum + Number(e.authorEarning), 0),
      averageCreditTransaction: creditEarnings.length > 0 
        ? creditEarnings.reduce((sum, e) => sum + Number(e.amount), 0) / creditEarnings.length 
        : 0,
      creditEarningsThisPeriod: periodEarnings
        .filter(e => e.type === EarningType.CREDIT_PURCHASE)
        .reduce((sum, e) => sum + Number(e.authorEarning), 0)
    }

    return NextResponse.json({
      summary: {
        totalEarnings,
        totalRevenue,
        totalPlatformFees,
        unpaidAmount,
        totalTransactions: allEarnings.length,
        period: {
          days: parseInt(period),
          earnings: periodTotalEarnings,
          revenue: periodTotalRevenue,
          transactions: periodEarnings.length
        }
      },
      byType: {
        allTime: totalsByType,
        period: periodTotalsByType
      },
      creditMetrics,
      unpaidEarnings: unpaidEarnings.map(earning => ({
        id: earning.id,
        type: earning.type,
        amount: earning.amount,
        authorEarning: earning.authorEarning,
        description: earning.description,
        createdAt: earning.createdAt
      })),
      recentPayouts: recentPayouts.map(payout => ({
        id: payout.id,
        amount: payout.amount,
        status: payout.status,
        description: payout.description,
        createdAt: payout.createdAt,
        processedAt: payout.processedAt
      })),
      monthlyTrends: monthlyEarnings
    })
  } catch (error) {
    console.error("Error fetching earnings summary:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
