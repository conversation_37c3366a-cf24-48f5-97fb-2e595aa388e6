import { NextRequest, NextResponse } from 'next/server'
import { getGCSManager } from '@/lib/gcs'

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    const filename = decodeURIComponent(params.filename)
    const manager = await getGCSManager()
    
    // Check if file exists
    const fileExists = await manager.fileExists(filename)
    if (!fileExists) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Get signed URL for the file with longer expiry for chapter images
    const signedUrl = await manager.getSignedUrl(filename, 7200) // 2 hours expiry
    
    // Create response with caching headers
    const response = NextResponse.redirect(signedUrl)
    
    // Add caching headers for better performance
    response.headers.set('Cache-Control', 'public, max-age=3600, s-maxage=7200') // 1 hour browser, 2 hours CDN
    response.headers.set('Vary', 'Accept-Encoding')
    
    return response
  } catch (error) {
    console.error('Error serving chapter image:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
