import { NextRequest, NextResponse } from "next/server"
import { getGCSManager } from "@/lib/gcs"

export async function GET(request: NextRequest) {
  try {
    // Test GCS connection and chapter image functionality
    const manager = await getGCSManager()
    
    // Test bucket access
    const [files] = await manager.bucket.getFiles({
      prefix: 'chapters/',
      maxResults: 5,
    })
    
    const testResults = {
      success: true,
      timestamp: new Date().toISOString(),
      gcsConnection: "✅ Connected",
      bucketName: manager.bucket.name,
      chapterImagesFolder: "chapters/",
      sampleFiles: files.map(file => ({
        name: file.name,
        size: file.metadata.size,
        contentType: file.metadata.contentType,
        created: file.metadata.timeCreated,
      })),
      endpoints: {
        upload: "/api/upload/chapter-images",
        serve: "/api/images/chapter/[filename]",
        delete: "/api/upload/chapter-images?filename=...&chapterId=...",
      },
      features: [
        "✅ Secure image upload for chapters",
        "✅ Author-only permissions",
        "✅ Chapter ownership verification", 
        "✅ Markdown image embedding",
        "✅ Responsive image display",
        "✅ Click-to-zoom functionality",
        "✅ Lazy loading optimization",
        "✅ Proper caching headers",
      ]
    }
    
    return NextResponse.json(testResults)
  } catch (error) {
    console.error("GCS Chapter Images Test Error:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
      troubleshooting: [
        "Check GOOGLE_CLOUD_PROJECT_ID environment variable",
        "Check GOOGLE_CLOUD_BUCKET_NAME environment variable", 
        "Verify gcloud authentication: gcloud auth application-default login",
        "Ensure service account has Storage Admin permissions",
        "Check if bucket exists and is accessible",
      ]
    }, { status: 500 })
  }
}
