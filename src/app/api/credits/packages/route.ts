import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@prisma/client"
import { z } from "zod"

const createPackageSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  credits: z.number().int().min(1),
  price: z.number().min(0),
  bonusCredits: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().default(0),
})

// GET /api/credits/packages - Get available credit packages
export async function GET() {
  try {
    const packages = await prisma.creditPackage.findMany({
      where: {
        isActive: true
      },
      orderBy: {
        sortOrder: 'asc'
      }
    })

    return NextResponse.json({ packages })
  } catch (error) {
    console.error("Error fetching credit packages:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/credits/packages - Create a new credit package (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (!user || user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const body = await request.json()
    const packageData = createPackageSchema.parse(body)

    const creditPackage = await prisma.creditPackage.create({
      data: packageData
    })

    return NextResponse.json({
      message: "Credit package created successfully",
      package: creditPackage
    })
  } catch (error) {
    console.error("Error creating credit package:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
