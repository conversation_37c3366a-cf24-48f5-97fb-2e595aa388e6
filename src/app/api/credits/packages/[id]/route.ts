import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@prisma/client"
import { z } from "zod"

const updatePackageSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  credits: z.number().int().min(1).optional(),
  price: z.number().min(0).optional(),
  bonusCredits: z.number().int().min(0).optional(),
  isActive: z.boolean().optional(),
  sortOrder: z.number().int().optional(),
})

// GET /api/credits/packages/[id] - Get specific credit package
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const creditPackage = await prisma.creditPackage.findUnique({
      where: { id: params.id }
    })

    if (!creditPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 })
    }

    return NextResponse.json({ package: creditPackage })
  } catch (error) {
    console.error("Error fetching credit package:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/credits/packages/[id] - Update credit package (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (!user || user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const body = await request.json()
    const updateData = updatePackageSchema.parse(body)

    // Check if package exists
    const existingPackage = await prisma.creditPackage.findUnique({
      where: { id: params.id }
    })

    if (!existingPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 })
    }

    const updatedPackage = await prisma.creditPackage.update({
      where: { id: params.id },
      data: updateData
    })

    return NextResponse.json({
      message: "Credit package updated successfully",
      package: updatedPackage
    })
  } catch (error) {
    console.error("Error updating credit package:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/credits/packages/[id] - Delete credit package (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    })

    if (!user || user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Check if package exists
    const existingPackage = await prisma.creditPackage.findUnique({
      where: { id: params.id }
    })

    if (!existingPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 })
    }

    // Check if package has any purchases
    const purchaseCount = await prisma.creditPurchase.count({
      where: { packageId: params.id }
    })

    if (purchaseCount > 0) {
      return NextResponse.json(
        { 
          error: "Cannot delete package with existing purchases",
          details: `This package has ${purchaseCount} purchase(s). Consider deactivating instead.`
        },
        { status: 400 }
      )
    }

    await prisma.creditPackage.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      message: "Credit package deleted successfully"
    })
  } catch (error) {
    console.error("Error deleting credit package:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
