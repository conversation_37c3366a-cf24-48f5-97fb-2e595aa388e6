import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { stripe } from "@/lib/stripe"
import { PaymentStatus, CreditTransactionType, CreditTransactionStatus } from "@prisma/client"
import { z } from "zod"

const purchaseCreditsSchema = z.object({
  packageId: z.string(),
  paymentMethodId: z.string(),
})

// POST /api/credits/purchase - Purchase credits
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { packageId, paymentMethodId } = purchaseCreditsSchema.parse(body)

    // Get the credit package
    const creditPackage = await prisma.creditPackage.findUnique({
      where: { id: packageId }
    })

    if (!creditPackage || !creditPackage.isActive) {
      return NextResponse.json({ error: "Credit package not found" }, { status: 404 })
    }

    // Get or create Stripe customer
    let user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    let stripeCustomerId = user.stripeCustomerId

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name || undefined,
        metadata: {
          userId: user.id,
        },
      })

      stripeCustomerId = customer.id

      await prisma.user.update({
        where: { id: user.id },
        data: { stripeCustomerId },
      })
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(creditPackage.price.toNumber() * 100), // Convert to cents
      currency: creditPackage.currency,
      customer: stripeCustomerId,
      payment_method: paymentMethodId,
      confirmation_method: 'manual',
      confirm: true,
      return_url: `${process.env.NEXTAUTH_URL}/dashboard/credits`,
      metadata: {
        type: 'credit_purchase',
        packageId: creditPackage.id,
        userId: user.id,
        credits: creditPackage.credits.toString(),
        bonusCredits: creditPackage.bonusCredits.toString(),
      },
    })

    // Create credit purchase record
    const totalCredits = creditPackage.credits + creditPackage.bonusCredits
    
    const creditPurchase = await prisma.creditPurchase.create({
      data: {
        userId: session.user.id,
        packageId: creditPackage.id,
        credits: creditPackage.credits,
        bonusCredits: creditPackage.bonusCredits,
        totalCredits,
        amount: creditPackage.price,
        currency: creditPackage.currency,
        stripePaymentId: paymentIntent.id,
        status: paymentIntent.status === 'succeeded' ? PaymentStatus.COMPLETED : PaymentStatus.PENDING,
      },
      include: {
        package: true,
      }
    })

    // If payment succeeded, add credits to user balance
    if (paymentIntent.status === 'succeeded') {
      await processSuccessfulCreditPurchase(creditPurchase.id)
    }

    return NextResponse.json({
      purchase: creditPurchase,
      clientSecret: paymentIntent.client_secret,
      requiresAction: paymentIntent.status === 'requires_action',
    })
  } catch (error) {
    console.error("Error purchasing credits:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Helper function to process successful credit purchase
async function processSuccessfulCreditPurchase(purchaseId: string) {
  const purchase = await prisma.creditPurchase.findUnique({
    where: { id: purchaseId },
    include: { user: true, package: true }
  })

  if (!purchase) return

  const currentBalance = purchase.user.creditBalance
  const newBalance = currentBalance + purchase.totalCredits

  // Update user balance and create transaction record in a transaction
  await prisma.$transaction([
    // Update user credit balance
    prisma.user.update({
      where: { id: purchase.userId },
      data: { creditBalance: newBalance }
    }),
    
    // Create credit transaction record
    prisma.creditTransaction.create({
      data: {
        userId: purchase.userId,
        type: CreditTransactionType.PURCHASE,
        status: CreditTransactionStatus.COMPLETED,
        amount: purchase.totalCredits,
        description: `Purchased ${purchase.credits} credits${purchase.bonusCredits > 0 ? ` + ${purchase.bonusCredits} bonus` : ''} (${purchase.package.name})`,
        sourceType: 'purchase',
        sourceId: purchase.id,
        balanceBefore: currentBalance,
        balanceAfter: newBalance,
        purchaseId: purchase.id,
      }
    })
  ])
}
