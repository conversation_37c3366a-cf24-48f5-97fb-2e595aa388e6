import { NextRequest, NextResponse } from "next/server"
import { CreditNotificationService } from "@/lib/notifications/credit-notifications"

// POST /api/cron/low-balance-check - Check and send low balance warnings
export async function POST(request: NextRequest) {
  try {
    // Verify this is a legitimate cron request
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log('Starting low balance check...')
    
    await CreditNotificationService.checkAndSendLowBalanceWarnings()
    
    console.log('Low balance check completed')
    
    return NextResponse.json({ 
      success: true, 
      message: "Low balance check completed",
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error("Error in low balance check:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// GET /api/cron/low-balance-check - Health check
export async function GET() {
  return NextResponse.json({ 
    status: "healthy",
    service: "low-balance-check",
    timestamp: new Date().toISOString()
  })
}
