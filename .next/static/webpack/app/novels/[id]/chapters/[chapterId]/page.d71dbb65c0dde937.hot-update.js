"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/chapters/[chapterId]/page",{

/***/ "(app-pages-browser)/./src/components/chapter/chapter-reader.tsx":
/*!***************************************************!*\
  !*** ./src/components/chapter/chapter-reader.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChapterReader: function() { return /* binding */ ChapterReader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* harmony import */ var _components_library_library_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/library/library-button */ \"(app-pages-browser)/./src/components/library/library-button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChapterReader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChapterReader(param) {\n    let { chapter, novelId } = param;\n    var _chapter_novel;\n    _s();\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch all chapters for navigation\n    const { data: chapters } = (0,_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery)(novelId);\n    var _chapters_findIndex;\n    // Find current chapter index and navigation\n    const currentChapterIndex = (_chapters_findIndex = chapters === null || chapters === void 0 ? void 0 : chapters.findIndex((ch)=>ch.id === chapter.id)) !== null && _chapters_findIndex !== void 0 ? _chapters_findIndex : -1;\n    const previousChapter = currentChapterIndex > 0 ? chapters === null || chapters === void 0 ? void 0 : chapters[currentChapterIndex - 1] : null;\n    var _chapters_length;\n    const nextChapter = currentChapterIndex >= 0 && currentChapterIndex < ((_chapters_length = chapters === null || chapters === void 0 ? void 0 : chapters.length) !== null && _chapters_length !== void 0 ? _chapters_length : 0) - 1 ? chapters === null || chapters === void 0 ? void 0 : chapters[currentChapterIndex + 1] : null;\n    // Load reading preferences from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedFontSize = localStorage.getItem(\"reader-font-size\");\n        const savedTheme = localStorage.getItem(\"reader-theme\");\n        if (savedFontSize) setFontSize(parseInt(savedFontSize));\n        if (savedTheme) setTheme(savedTheme);\n    }, []);\n    // Save preferences to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"reader-font-size\", fontSize.toString());\n        localStorage.setItem(\"reader-theme\", theme);\n    }, [\n        fontSize,\n        theme\n    ]);\n    const themeClasses = {\n        light: \"bg-white text-gray-900\",\n        dark: \"bg-gray-900 text-gray-100\",\n        sepia: \"bg-amber-50 text-amber-900\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen transition-colors \".concat(themeClasses[theme]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/novels/\".concat(novelId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Novel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-semibold truncate max-w-md\",\n                                                children: ((_chapter_novel = chapter.novel) === null || _chapter_novel === void 0 ? void 0 : _chapter_novel.title) || \"Novel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Chapter \",\n                                                    chapter.order,\n                                                    \": \",\n                                                    chapter.title\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_library_library_button__WEBPACK_IMPORTED_MODULE_7__.LibraryButton, {\n                                        novelId: novelId,\n                                        variant: \"ghost\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowSettings(!showSettings),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-muted/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Font Size:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.max(12, fontSize - 2)),\n                                                disabled: fontSize <= 12,\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-8 text-center text-sm\",\n                                                children: fontSize\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.min(24, fontSize + 2)),\n                                                disabled: fontSize >= 24,\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Theme:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            \"light\",\n                                            \"dark\",\n                                            \"sepia\"\n                                        ].map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: theme === t ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setTheme(t),\n                                                className: \"capitalize\",\n                                                children: t\n                                            }, t, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8 max-w-4xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"text-center space-y-4 pb-8 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mb-2\",\n                                        children: [\n                                            \"Chapter \",\n                                            chapter.order\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold tracking-tight\",\n                                        children: chapter.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"Published \",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(chapter.createdAt)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose-reading custom-scrollbar\",\n                            style: {\n                                fontSize: \"\".concat(fontSize, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap leading-relaxed\",\n                                dangerouslySetInnerHTML: {\n                                    __html: chapter.content\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"pt-8 border-t space-y-4\",\n                            children: [\n                                chapters && chapters.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-2\",\n                                            children: [\n                                                \"Chapter \",\n                                                currentChapterIndex + 1,\n                                                \" of \",\n                                                chapters.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-muted rounded-full h-2 max-w-xs mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-2 rounded-full transition-all\",\n                                                style: {\n                                                    width: \"\".concat((currentChapterIndex + 1) / chapters.length * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        previousChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(previousChapter.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Chapter \",\n                                                                    previousChapter.order\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Previous\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Chapter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Back to Novel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        nextChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(nextChapter.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Chapter \",\n                                                                    nextChapter.order\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"ml-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Next\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Chapter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(ChapterReader, \"7PPzi0MNZ+c2C9Y2Ce/VmsEGfBo=\", false, function() {\n    return [\n        _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery\n    ];\n});\n_c = ChapterReader;\nvar _c;\n$RefreshReg$(_c, \"ChapterReader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chapter/chapter-reader.tsx\n"));

/***/ })

});