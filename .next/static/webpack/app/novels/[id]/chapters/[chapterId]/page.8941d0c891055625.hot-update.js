"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/chapters/[chapterId]/page",{

/***/ "(app-pages-browser)/./src/components/chapter/chapter-reader.tsx":
/*!***************************************************!*\
  !*** ./src/components/chapter/chapter-reader.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChapterReader: function() { return /* binding */ ChapterReader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* harmony import */ var _components_library_library_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/library/library-button */ \"(app-pages-browser)/./src/components/library/library-button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChapterReader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChapterReader(param) {\n    let { chapter, novelId } = param;\n    var _chapter_novel;\n    _s();\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch all chapters for navigation\n    const { data: chapters } = (0,_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery)(novelId);\n    // Find current chapter index and navigation\n    const currentChapterIndex = Array.isArray(chapters) ? chapters.findIndex((ch)=>ch.id === chapter.id) : -1;\n    const previousChapter = currentChapterIndex > 0 && Array.isArray(chapters) ? chapters[currentChapterIndex - 1] : null;\n    const nextChapter = currentChapterIndex >= 0 && Array.isArray(chapters) && currentChapterIndex < chapters.length - 1 ? chapters[currentChapterIndex + 1] : null;\n    // Load reading preferences from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedFontSize = localStorage.getItem(\"reader-font-size\");\n        const savedTheme = localStorage.getItem(\"reader-theme\");\n        if (savedFontSize) setFontSize(parseInt(savedFontSize));\n        if (savedTheme) setTheme(savedTheme);\n    }, []);\n    // Save preferences to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"reader-font-size\", fontSize.toString());\n        localStorage.setItem(\"reader-theme\", theme);\n    }, [\n        fontSize,\n        theme\n    ]);\n    const themeClasses = {\n        light: \"bg-white text-gray-900\",\n        dark: \"bg-gray-900 text-gray-100\",\n        sepia: \"bg-amber-50 text-amber-900\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen transition-colors \".concat(themeClasses[theme]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/novels/\".concat(novelId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Novel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-semibold truncate max-w-md\",\n                                                children: ((_chapter_novel = chapter.novel) === null || _chapter_novel === void 0 ? void 0 : _chapter_novel.title) || \"Novel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Chapter \",\n                                                    chapter.order,\n                                                    \": \",\n                                                    chapter.title\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_library_library_button__WEBPACK_IMPORTED_MODULE_7__.LibraryButton, {\n                                        novelId: novelId,\n                                        variant: \"ghost\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowSettings(!showSettings),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-muted/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Font Size:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.max(12, fontSize - 2)),\n                                                disabled: fontSize <= 12,\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-8 text-center text-sm\",\n                                                children: fontSize\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.min(24, fontSize + 2)),\n                                                disabled: fontSize >= 24,\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Theme:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            \"light\",\n                                            \"dark\",\n                                            \"sepia\"\n                                        ].map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: theme === t ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setTheme(t),\n                                                className: \"capitalize\",\n                                                children: t\n                                            }, t, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8 max-w-4xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"text-center space-y-4 pb-8 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mb-2\",\n                                        children: [\n                                            \"Chapter \",\n                                            chapter.order\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold tracking-tight\",\n                                        children: chapter.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"Published \",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(chapter.createdAt)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose-reading custom-scrollbar\",\n                            style: {\n                                fontSize: \"\".concat(fontSize, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap leading-relaxed\",\n                                dangerouslySetInnerHTML: {\n                                    __html: chapter.content\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"pt-8 border-t space-y-4\",\n                            children: [\n                                chapters && chapters.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-2\",\n                                            children: [\n                                                \"Chapter \",\n                                                currentChapterIndex + 1,\n                                                \" of \",\n                                                chapters.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-muted rounded-full h-2 max-w-xs mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-2 rounded-full transition-all\",\n                                                style: {\n                                                    width: \"\".concat((currentChapterIndex + 1) / chapters.length * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        previousChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(previousChapter.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Chapter \",\n                                                                    previousChapter.order\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Previous\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Chapter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Back to Novel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        nextChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(nextChapter.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Chapter \",\n                                                                    nextChapter.order\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"ml-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Next\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Chapter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(ChapterReader, \"7PPzi0MNZ+c2C9Y2Ce/VmsEGfBo=\", false, function() {\n    return [\n        _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery\n    ];\n});\n_c = ChapterReader;\nvar _c;\n$RefreshReg$(_c, \"ChapterReader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXB0ZXIvY2hhcHRlci1yZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDZjtBQUNtQjtBQUVGO0FBYXhCO0FBQ21CO0FBQ3FCO0FBQ007QUFtQjVELFNBQVNlLGNBQWMsS0FBd0M7UUFBeEMsRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQXNCLEdBQXhDO1FBb0RYRDs7SUFuRGpCLE1BQU0sQ0FBQ0UsVUFBVUMsWUFBWSxHQUFHbkIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDb0IsT0FBT0MsU0FBUyxHQUFHckIsK0NBQVFBLENBQTZCO0lBQy9ELE1BQU0sQ0FBQ3NCLGNBQWNDLGdCQUFnQixHQUFHdkIsK0NBQVFBLENBQUM7SUFFakQsb0NBQW9DO0lBQ3BDLE1BQU0sRUFBRXdCLE1BQU1DLFFBQVEsRUFBRSxHQUFHWiwyRUFBbUJBLENBQUNJO0lBRS9DLDRDQUE0QztJQUM1QyxNQUFNUyxzQkFBc0JDLE1BQU1DLE9BQU8sQ0FBQ0gsWUFBWUEsU0FBU0ksU0FBUyxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHQyxFQUFFLEtBQUtmLFFBQVFlLEVBQUUsSUFBSSxDQUFDO0lBQ3hHLE1BQU1DLGtCQUFrQk4sc0JBQXNCLEtBQUtDLE1BQU1DLE9BQU8sQ0FBQ0gsWUFBWUEsUUFBUSxDQUFDQyxzQkFBc0IsRUFBRSxHQUFHO0lBQ2pILE1BQU1PLGNBQWNQLHVCQUF1QixLQUFLQyxNQUFNQyxPQUFPLENBQUNILGFBQWFDLHNCQUFzQkQsU0FBU1MsTUFBTSxHQUFHLElBQy9HVCxRQUFRLENBQUNDLHNCQUFzQixFQUFFLEdBQ2pDO0lBRUosNkNBQTZDO0lBQzdDekIsZ0RBQVNBLENBQUM7UUFDUixNQUFNa0MsZ0JBQWdCQyxhQUFhQyxPQUFPLENBQUM7UUFDM0MsTUFBTUMsYUFBYUYsYUFBYUMsT0FBTyxDQUFDO1FBRXhDLElBQUlGLGVBQWVoQixZQUFZb0IsU0FBU0o7UUFDeEMsSUFBSUcsWUFBWWpCLFNBQVNpQjtJQUMzQixHQUFHLEVBQUU7SUFFTCxtQ0FBbUM7SUFDbkNyQyxnREFBU0EsQ0FBQztRQUNSbUMsYUFBYUksT0FBTyxDQUFDLG9CQUFvQnRCLFNBQVN1QixRQUFRO1FBQzFETCxhQUFhSSxPQUFPLENBQUMsZ0JBQWdCcEI7SUFDdkMsR0FBRztRQUFDRjtRQUFVRTtLQUFNO0lBRXBCLE1BQU1zQixlQUFlO1FBQ25CQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVcsa0NBQXNELE9BQXBCTCxZQUFZLENBQUN0QixNQUFNOzswQkFFbkUsOERBQUM0QjtnQkFBT0QsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzVDLHlEQUFNQTt3Q0FBQzhDLFNBQVE7d0NBQVFDLE1BQUs7d0NBQUtDLE9BQU87a0RBQ3ZDLDRFQUFDakQsaURBQUlBOzRDQUFDa0QsTUFBTSxXQUFtQixPQUFSbkM7OzhEQUNyQiw4REFBQ1osNklBQVNBO29EQUFDMEMsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7Ozs7Ozs7O2tEQUsxQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTTtnREFBR04sV0FBVTswREFDWC9CLEVBQUFBLGlCQUFBQSxRQUFRc0MsS0FBSyxjQUFidEMscUNBQUFBLGVBQWV1QyxLQUFLLEtBQUk7Ozs7OzswREFFM0IsOERBQUNDO2dEQUFFVCxXQUFVOztvREFBZ0M7b0RBQ2xDL0IsUUFBUXlDLEtBQUs7b0RBQUM7b0RBQUd6QyxRQUFRdUMsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLN0MsOERBQUNUO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2pDLDZFQUFhQTt3Q0FDWkcsU0FBU0E7d0NBQ1RnQyxTQUFRO3dDQUNSQyxNQUFLOzs7Ozs7a0RBR1AsOERBQUMvQyx5REFBTUE7d0NBQ0w4QyxTQUFRO3dDQUNSQyxNQUFLO3dDQUNMUSxTQUFTLElBQU1uQyxnQkFBZ0IsQ0FBQ0Q7a0RBRWhDLDRFQUFDZiw2SUFBUUE7NENBQUN3QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRN0J6Qiw4QkFDQyw4REFBQ3dCO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNyQyw4SUFBSUE7d0NBQUNxQyxXQUFVOzs7Ozs7a0RBQ2hCLDhEQUFDWTt3Q0FBS1osV0FBVTtrREFBc0I7Ozs7OztrREFDdEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzVDLHlEQUFNQTtnREFDTDhDLFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xRLFNBQVMsSUFBTXZDLFlBQVl5QyxLQUFLQyxHQUFHLENBQUMsSUFBSTNDLFdBQVc7Z0RBQ25ENEMsVUFBVTVDLFlBQVk7MERBQ3ZCOzs7Ozs7MERBR0QsOERBQUN5QztnREFBS1osV0FBVTswREFBMkI3Qjs7Ozs7OzBEQUMzQyw4REFBQ2YseURBQU1BO2dEQUNMOEMsU0FBUTtnREFDUkMsTUFBSztnREFDTFEsU0FBUyxJQUFNdkMsWUFBWXlDLEtBQUtHLEdBQUcsQ0FBQyxJQUFJN0MsV0FBVztnREFDbkQ0QyxVQUFVNUMsWUFBWTswREFDdkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPTCw4REFBQzRCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3BDLDhJQUFPQTt3Q0FBQ29DLFdBQVU7Ozs7OztrREFDbkIsOERBQUNZO3dDQUFLWixXQUFVO2tEQUFzQjs7Ozs7O2tEQUN0Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1o7NENBQUU7NENBQVM7NENBQVE7eUNBQVEsQ0FBV2lCLEdBQUcsQ0FBQyxDQUFDQyxrQkFDMUMsOERBQUM5RCx5REFBTUE7Z0RBRUw4QyxTQUFTN0IsVUFBVTZDLElBQUksWUFBWTtnREFDbkNmLE1BQUs7Z0RBQ0xRLFNBQVMsSUFBTXJDLFNBQVM0QztnREFDeEJsQixXQUFVOzBEQUVUa0I7K0NBTklBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFpQnJCLDhEQUFDQztnQkFBS25CLFdBQVU7MEJBQ2QsNEVBQUNvQjtvQkFBUXBCLFdBQVU7O3NDQUVqQiw4REFBQ0M7NEJBQU9ELFdBQVU7c0NBQ2hCLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMzQyx1REFBS0E7d0NBQUM2QyxTQUFRO3dDQUFVRixXQUFVOzs0Q0FBTzs0Q0FDL0IvQixRQUFReUMsS0FBSzs7Ozs7OztrREFFeEIsOERBQUNKO3dDQUFHTixXQUFVO2tEQUNYL0IsUUFBUXVDLEtBQUs7Ozs7OztrREFFaEIsOERBQUNDO3dDQUFFVCxXQUFVOzs0Q0FBd0I7NENBQ3hCbkMsc0RBQVVBLENBQUNJLFFBQVFvRCxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTdDLDhEQUFDdEI7NEJBQ0NDLFdBQVU7NEJBQ1ZzQixPQUFPO2dDQUFFbkQsVUFBVSxHQUFZLE9BQVRBLFVBQVM7NEJBQUk7c0NBRW5DLDRFQUFDNEI7Z0NBQ0NDLFdBQVU7Z0NBQ1Z1Qix5QkFBeUI7b0NBQUVDLFFBQVF2RCxRQUFRd0QsT0FBTztnQ0FBQzs7Ozs7Ozs7Ozs7c0NBS3ZELDhEQUFDQzs0QkFBTzFCLFdBQVU7O2dDQUVmdEIsWUFBWUEsU0FBU1MsTUFBTSxHQUFHLG1CQUM3Qiw4REFBQ1k7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDUzs0Q0FBRVQsV0FBVTs7Z0RBQXFDO2dEQUN2Q3JCLHNCQUFzQjtnREFBRTtnREFBS0QsU0FBU1MsTUFBTTs7Ozs7OztzREFFdkQsOERBQUNZOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFDQ0MsV0FBVTtnREFDVnNCLE9BQU87b0RBQUVLLE9BQU8sR0FBdUQsT0FBcEQsQ0FBRWhELHNCQUFzQixLQUFLRCxTQUFTUyxNQUFNLEdBQUksS0FBSTtnREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT2xGLDhEQUFDWTtvQ0FBSUMsV0FBVTs7d0NBQ1pmLGdDQUNDLDhEQUFDN0IseURBQU1BOzRDQUFDOEMsU0FBUTs0Q0FBVUUsT0FBTztzREFDL0IsNEVBQUNqRCxpREFBSUE7Z0RBQUNrRCxNQUFNLFdBQStCcEIsT0FBcEJmLFNBQVEsY0FBK0IsT0FBbkJlLGdCQUFnQkQsRUFBRTs7a0VBQzNELDhEQUFDdkIsOElBQVdBO3dEQUFDdUMsV0FBVTs7Ozs7O2tFQUN2Qiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFBZ0M7Ozs7OzswRUFDL0MsOERBQUNEO2dFQUFJQyxXQUFVOztvRUFBYztvRUFBU2YsZ0JBQWdCeUIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7aUVBS2pFLDhEQUFDdEQseURBQU1BOzRDQUFDOEMsU0FBUTs0Q0FBVWEsUUFBUTs7OERBQ2hDLDhEQUFDdEQsOElBQVdBO29EQUFDdUMsV0FBVTs7Ozs7OzhEQUN2Qiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBZ0M7Ozs7OztzRUFDL0MsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS25DLDhEQUFDNUMseURBQU1BOzRDQUFDZ0QsT0FBTztzREFDYiw0RUFBQ2pELGlEQUFJQTtnREFBQ2tELE1BQU0sV0FBbUIsT0FBUm5DOztrRUFDckIsOERBQUNYLDhJQUFRQTt3REFBQ3lDLFdBQVU7Ozs7OztvREFBaUI7Ozs7Ozs7Ozs7Ozt3Q0FLeENkLDRCQUNDLDhEQUFDOUIseURBQU1BOzRDQUFDOEMsU0FBUTs0Q0FBVUUsT0FBTztzREFDL0IsNEVBQUNqRCxpREFBSUE7Z0RBQUNrRCxNQUFNLFdBQStCbkIsT0FBcEJoQixTQUFRLGNBQTJCLE9BQWZnQixZQUFZRixFQUFFOztrRUFDdkQsOERBQUNlO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQWdDOzs7Ozs7MEVBQy9DLDhEQUFDRDtnRUFBSUMsV0FBVTs7b0VBQWM7b0VBQVNkLFlBQVl3QixLQUFLOzs7Ozs7Ozs7Ozs7O2tFQUV6RCw4REFBQ2hELDhJQUFZQTt3REFBQ3NDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7aUVBSTVCLDhEQUFDNUMseURBQU1BOzRDQUFDOEMsU0FBUTs0Q0FBVWEsUUFBUTs7OERBQ2hDLDhEQUFDaEI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBZ0M7Ozs7OztzRUFDL0MsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFjOzs7Ozs7Ozs7Ozs7OERBRS9CLDhEQUFDdEMsOElBQVlBO29EQUFDc0MsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTMUM7R0F4T2dCaEM7O1FBTWFGLHVFQUFtQkE7OztLQU5oQ0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvY2hhcHRlci9jaGFwdGVyLXJlYWRlci50c3g/ZjRhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHtcbiAgQXJyb3dMZWZ0LFxuICBBcnJvd1JpZ2h0LFxuICBCb29rT3BlbixcbiAgU2V0dGluZ3MsXG4gIENoZXZyb25MZWZ0LFxuICBDaGV2cm9uUmlnaHQsXG4gIFR5cGUsXG4gIFBhbGV0dGUsXG4gIEhlYXJ0LFxuICBTaGFyZSxcbiAgQm9va21hcmtcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyBmb3JtYXREYXRlIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcbmltcG9ydCB7IHVzZUdldENoYXB0ZXJzUXVlcnkgfSBmcm9tIFwiQC9zdG9yZS9hcGkvY2hhcHRlcnNBcGlcIlxuaW1wb3J0IHsgTGlicmFyeUJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvbGlicmFyeS9saWJyYXJ5LWJ1dHRvblwiXG5cbmludGVyZmFjZSBDaGFwdGVyIHtcbiAgaWQ6IHN0cmluZ1xuICB0aXRsZTogc3RyaW5nXG4gIGNvbnRlbnQ6IHN0cmluZ1xuICBvcmRlcjogbnVtYmVyXG4gIGNyZWF0ZWRBdDogc3RyaW5nIHwgRGF0ZVxuICBub3ZlbD86IHtcbiAgICBpZDogc3RyaW5nXG4gICAgdGl0bGU6IHN0cmluZ1xuICB9XG59XG5cbmludGVyZmFjZSBDaGFwdGVyUmVhZGVyUHJvcHMge1xuICBjaGFwdGVyOiBDaGFwdGVyXG4gIG5vdmVsSWQ6IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQ2hhcHRlclJlYWRlcih7IGNoYXB0ZXIsIG5vdmVsSWQgfTogQ2hhcHRlclJlYWRlclByb3BzKSB7XG4gIGNvbnN0IFtmb250U2l6ZSwgc2V0Rm9udFNpemVdID0gdXNlU3RhdGUoMTYpXG4gIGNvbnN0IFt0aGVtZSwgc2V0VGhlbWVdID0gdXNlU3RhdGU8XCJsaWdodFwiIHwgXCJkYXJrXCIgfCBcInNlcGlhXCI+KFwibGlnaHRcIilcbiAgY29uc3QgW3Nob3dTZXR0aW5ncywgc2V0U2hvd1NldHRpbmdzXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8vIEZldGNoIGFsbCBjaGFwdGVycyBmb3IgbmF2aWdhdGlvblxuICBjb25zdCB7IGRhdGE6IGNoYXB0ZXJzIH0gPSB1c2VHZXRDaGFwdGVyc1F1ZXJ5KG5vdmVsSWQpXG5cbiAgLy8gRmluZCBjdXJyZW50IGNoYXB0ZXIgaW5kZXggYW5kIG5hdmlnYXRpb25cbiAgY29uc3QgY3VycmVudENoYXB0ZXJJbmRleCA9IEFycmF5LmlzQXJyYXkoY2hhcHRlcnMpID8gY2hhcHRlcnMuZmluZEluZGV4KGNoID0+IGNoLmlkID09PSBjaGFwdGVyLmlkKSA6IC0xXG4gIGNvbnN0IHByZXZpb3VzQ2hhcHRlciA9IGN1cnJlbnRDaGFwdGVySW5kZXggPiAwICYmIEFycmF5LmlzQXJyYXkoY2hhcHRlcnMpID8gY2hhcHRlcnNbY3VycmVudENoYXB0ZXJJbmRleCAtIDFdIDogbnVsbFxuICBjb25zdCBuZXh0Q2hhcHRlciA9IGN1cnJlbnRDaGFwdGVySW5kZXggPj0gMCAmJiBBcnJheS5pc0FycmF5KGNoYXB0ZXJzKSAmJiBjdXJyZW50Q2hhcHRlckluZGV4IDwgY2hhcHRlcnMubGVuZ3RoIC0gMSBcbiAgICA/IGNoYXB0ZXJzW2N1cnJlbnRDaGFwdGVySW5kZXggKyAxXSBcbiAgICA6IG51bGxcblxuICAvLyBMb2FkIHJlYWRpbmcgcHJlZmVyZW5jZXMgZnJvbSBsb2NhbFN0b3JhZ2VcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBzYXZlZEZvbnRTaXplID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJyZWFkZXItZm9udC1zaXplXCIpXG4gICAgY29uc3Qgc2F2ZWRUaGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwicmVhZGVyLXRoZW1lXCIpXG5cbiAgICBpZiAoc2F2ZWRGb250U2l6ZSkgc2V0Rm9udFNpemUocGFyc2VJbnQoc2F2ZWRGb250U2l6ZSkpXG4gICAgaWYgKHNhdmVkVGhlbWUpIHNldFRoZW1lKHNhdmVkVGhlbWUgYXMgXCJsaWdodFwiIHwgXCJkYXJrXCIgfCBcInNlcGlhXCIpXG4gIH0sIFtdKVxuXG4gIC8vIFNhdmUgcHJlZmVyZW5jZXMgdG8gbG9jYWxTdG9yYWdlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJyZWFkZXItZm9udC1zaXplXCIsIGZvbnRTaXplLnRvU3RyaW5nKCkpXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJyZWFkZXItdGhlbWVcIiwgdGhlbWUpXG4gIH0sIFtmb250U2l6ZSwgdGhlbWVdKVxuXG4gIGNvbnN0IHRoZW1lQ2xhc3NlcyA9IHtcbiAgICBsaWdodDogXCJiZy13aGl0ZSB0ZXh0LWdyYXktOTAwXCIsXG4gICAgZGFyazogXCJiZy1ncmF5LTkwMCB0ZXh0LWdyYXktMTAwXCIsXG4gICAgc2VwaWE6IFwiYmctYW1iZXItNTAgdGV4dC1hbWJlci05MDBcIlxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YG1pbi1oLXNjcmVlbiB0cmFuc2l0aW9uLWNvbG9ycyAke3RoZW1lQ2xhc3Nlc1t0aGVtZV19YH0+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJzdGlja3kgdG9wLTAgei01MCBib3JkZXItYiBiZy1iYWNrZ3JvdW5kLzk1IGJhY2tkcm9wLWJsdXIgc3VwcG9ydHMtW2JhY2tkcm9wLWZpbHRlcl06YmctYmFja2dyb3VuZC82MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPXtgL25vdmVscy8ke25vdmVsSWR9YH0+XG4gICAgICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICBCYWNrIHRvIE5vdmVsXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRydW5jYXRlIG1heC13LW1kXCI+XG4gICAgICAgICAgICAgICAgICB7Y2hhcHRlci5ub3ZlbD8udGl0bGUgfHwgXCJOb3ZlbFwifVxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgIENoYXB0ZXIge2NoYXB0ZXIub3JkZXJ9OiB7Y2hhcHRlci50aXRsZX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPExpYnJhcnlCdXR0b25cbiAgICAgICAgICAgICAgICBub3ZlbElkPXtub3ZlbElkfVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1NldHRpbmdzKCFzaG93U2V0dGluZ3MpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvaGVhZGVyPlxuXG4gICAgICB7LyogUmVhZGluZyBTZXR0aW5ncyBQYW5lbCAqL31cbiAgICAgIHtzaG93U2V0dGluZ3MgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJnLW11dGVkLzUwIHAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gbWF4LXctNHhsXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGl0ZW1zLWNlbnRlciBnYXAtNlwiPlxuICAgICAgICAgICAgICB7LyogRm9udCBTaXplICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPFR5cGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkZvbnQgU2l6ZTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEZvbnRTaXplKE1hdGgubWF4KDEyLCBmb250U2l6ZSAtIDIpKX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2ZvbnRTaXplIDw9IDEyfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAtXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctOCB0ZXh0LWNlbnRlciB0ZXh0LXNtXCI+e2ZvbnRTaXplfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRGb250U2l6ZShNYXRoLm1pbigyNCwgZm9udFNpemUgKyAyKSl9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtmb250U2l6ZSA+PSAyNH1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgK1xuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBUaGVtZSAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxQYWxldHRlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5UaGVtZTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICB7KFtcImxpZ2h0XCIsIFwiZGFya1wiLCBcInNlcGlhXCJdIGFzIGNvbnN0KS5tYXAoKHQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGtleT17dH1cbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXt0aGVtZSA9PT0gdCA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRUaGVtZSh0KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjYXBpdGFsaXplXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHt0fVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBDaGFwdGVyIENvbnRlbnQgKi99XG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LTggbWF4LXctNHhsXCI+XG4gICAgICAgIDxhcnRpY2xlIGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgIHsvKiBDaGFwdGVyIEhlYWRlciAqL31cbiAgICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNCBwYi04IGJvcmRlci1iXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJtYi0yXCI+XG4gICAgICAgICAgICAgICAgQ2hhcHRlciB7Y2hhcHRlci5vcmRlcn1cbiAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodFwiPlxuICAgICAgICAgICAgICAgIHtjaGFwdGVyLnRpdGxlfVxuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICBQdWJsaXNoZWQge2Zvcm1hdERhdGUoY2hhcHRlci5jcmVhdGVkQXQpfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICAgIHsvKiBDaGFwdGVyIENvbnRlbnQgKi99XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHJvc2UtcmVhZGluZyBjdXN0b20tc2Nyb2xsYmFyXCJcbiAgICAgICAgICAgIHN0eWxlPXt7IGZvbnRTaXplOiBgJHtmb250U2l6ZX1weGAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIndoaXRlc3BhY2UtcHJlLXdyYXAgbGVhZGluZy1yZWxheGVkXCJcbiAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBjaGFwdGVyLmNvbnRlbnQgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQ2hhcHRlciBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwicHQtOCBib3JkZXItdCBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIHsvKiBDaGFwdGVyIFByb2dyZXNzICovfVxuICAgICAgICAgICAge2NoYXB0ZXJzICYmIGNoYXB0ZXJzLmxlbmd0aCA+IDEgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgQ2hhcHRlciB7Y3VycmVudENoYXB0ZXJJbmRleCArIDF9IG9mIHtjaGFwdGVycy5sZW5ndGh9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLW11dGVkIHJvdW5kZWQtZnVsbCBoLTIgbWF4LXcteHMgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHJpbWFyeSBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsXCJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAkeygoY3VycmVudENoYXB0ZXJJbmRleCArIDEpIC8gY2hhcHRlcnMubGVuZ3RoKSAqIDEwMH0lYCB9fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7LyogTmF2aWdhdGlvbiBCdXR0b25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAge3ByZXZpb3VzQ2hhcHRlciA/IChcbiAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2Avbm92ZWxzLyR7bm92ZWxJZH0vY2hhcHRlcnMvJHtwcmV2aW91c0NoYXB0ZXIuaWR9YH0+XG4gICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uTGVmdCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5QcmV2aW91czwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5DaGFwdGVyIHtwcmV2aW91c0NoYXB0ZXIub3JkZXJ9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBkaXNhYmxlZD5cbiAgICAgICAgICAgICAgICAgIDxDaGV2cm9uTGVmdCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlByZXZpb3VzPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5DaGFwdGVyPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICA8QnV0dG9uIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj17YC9ub3ZlbHMvJHtub3ZlbElkfWB9PlxuICAgICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICBCYWNrIHRvIE5vdmVsXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgICB7bmV4dENoYXB0ZXIgPyAoXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPXtgL25vdmVscy8ke25vdmVsSWR9L2NoYXB0ZXJzLyR7bmV4dENoYXB0ZXIuaWR9YH0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5OZXh0PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPkNoYXB0ZXIge25leHRDaGFwdGVyLm9yZGVyfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJtbC0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBkaXNhYmxlZD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TmV4dDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+Q2hhcHRlcjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cIm1sLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Zvb3Rlcj5cbiAgICAgICAgPC9hcnRpY2xlPlxuICAgICAgPC9tYWluPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTGluayIsIkJ1dHRvbiIsIkJhZGdlIiwiQXJyb3dMZWZ0IiwiQm9va09wZW4iLCJTZXR0aW5ncyIsIkNoZXZyb25MZWZ0IiwiQ2hldnJvblJpZ2h0IiwiVHlwZSIsIlBhbGV0dGUiLCJmb3JtYXREYXRlIiwidXNlR2V0Q2hhcHRlcnNRdWVyeSIsIkxpYnJhcnlCdXR0b24iLCJDaGFwdGVyUmVhZGVyIiwiY2hhcHRlciIsIm5vdmVsSWQiLCJmb250U2l6ZSIsInNldEZvbnRTaXplIiwidGhlbWUiLCJzZXRUaGVtZSIsInNob3dTZXR0aW5ncyIsInNldFNob3dTZXR0aW5ncyIsImRhdGEiLCJjaGFwdGVycyIsImN1cnJlbnRDaGFwdGVySW5kZXgiLCJBcnJheSIsImlzQXJyYXkiLCJmaW5kSW5kZXgiLCJjaCIsImlkIiwicHJldmlvdXNDaGFwdGVyIiwibmV4dENoYXB0ZXIiLCJsZW5ndGgiLCJzYXZlZEZvbnRTaXplIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInNhdmVkVGhlbWUiLCJwYXJzZUludCIsInNldEl0ZW0iLCJ0b1N0cmluZyIsInRoZW1lQ2xhc3NlcyIsImxpZ2h0IiwiZGFyayIsInNlcGlhIiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwidmFyaWFudCIsInNpemUiLCJhc0NoaWxkIiwiaHJlZiIsImgxIiwibm92ZWwiLCJ0aXRsZSIsInAiLCJvcmRlciIsIm9uQ2xpY2siLCJzcGFuIiwiTWF0aCIsIm1heCIsImRpc2FibGVkIiwibWluIiwibWFwIiwidCIsIm1haW4iLCJhcnRpY2xlIiwiY3JlYXRlZEF0Iiwic3R5bGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsImNvbnRlbnQiLCJmb290ZXIiLCJ3aWR0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chapter/chapter-reader.tsx\n"));

/***/ })

});