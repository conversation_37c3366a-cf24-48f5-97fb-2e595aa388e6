"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/chapters/[chapterId]/page",{

/***/ "(app-pages-browser)/./src/components/chapter/chapter-reader.tsx":
/*!***************************************************!*\
  !*** ./src/components/chapter/chapter-reader.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChapterReader: function() { return /* binding */ ChapterReader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* harmony import */ var _components_library_library_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/library/library-button */ \"(app-pages-browser)/./src/components/library/library-button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ChapterReader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChapterReader(param) {\n    let { chapter, novelId } = param;\n    var _chapter_novel;\n    _s();\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch all chapters for navigation\n    const { data: chapters } = (0,_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery)(novelId);\n    // Find current chapter index and navigation\n    const currentChapterIndex = Array.isArray(chapters) ? chapters.findIndex((ch)=>ch.id === chapter.id) : -1;\n    const previousChapter = currentChapterIndex > 0 && Array.isArray(chapters) ? chapters[currentChapterIndex - 1] : null;\n    const nextChapter = currentChapterIndex >= 0 && Array.isArray(chapters) && currentChapterIndex < chapters.length - 1 ? chapters[currentChapterIndex + 1] : null;\n    // Load reading preferences from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedFontSize = localStorage.getItem(\"reader-font-size\");\n        const savedTheme = localStorage.getItem(\"reader-theme\");\n        if (savedFontSize) setFontSize(parseInt(savedFontSize));\n        if (savedTheme) setTheme(savedTheme);\n    }, []);\n    // Save preferences to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"reader-font-size\", fontSize.toString());\n        localStorage.setItem(\"reader-theme\", theme);\n    }, [\n        fontSize,\n        theme\n    ]);\n    const themeClasses = {\n        light: \"bg-white text-gray-900\",\n        dark: \"bg-gray-900 text-gray-100\",\n        sepia: \"bg-amber-50 text-amber-900\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen transition-colors \".concat(themeClasses[theme]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/novels/\".concat(novelId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Novel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-semibold truncate max-w-md\",\n                                                children: ((_chapter_novel = chapter.novel) === null || _chapter_novel === void 0 ? void 0 : _chapter_novel.title) || \"Novel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Chapter \",\n                                                    chapter.order,\n                                                    \": \",\n                                                    chapter.title\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_library_library_button__WEBPACK_IMPORTED_MODULE_7__.LibraryButton, {\n                                        novelId: novelId,\n                                        variant: \"ghost\",\n                                        size: \"sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowSettings(!showSettings),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-muted/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Font Size:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.max(12, fontSize - 2)),\n                                                disabled: fontSize <= 12,\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-8 text-center text-sm\",\n                                                children: fontSize\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.min(24, fontSize + 2)),\n                                                disabled: fontSize >= 24,\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Theme:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            \"light\",\n                                            \"dark\",\n                                            \"sepia\"\n                                        ].map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: theme === t ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setTheme(t),\n                                                className: \"capitalize\",\n                                                children: t\n                                            }, t, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8 max-w-4xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"text-center space-y-4 pb-8 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mb-2\",\n                                        children: [\n                                            \"Chapter \",\n                                            chapter.order\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold tracking-tight\",\n                                        children: chapter.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"Published \",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(chapter.createdAt)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose-reading custom-scrollbar\",\n                            style: {\n                                fontSize: \"\".concat(fontSize, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap leading-relaxed\",\n                                dangerouslySetInnerHTML: {\n                                    __html: chapter.content\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"pt-8 border-t space-y-4\",\n                            children: [\n                                Array.isArray(chapters) && chapters.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-2\",\n                                            children: [\n                                                \"Chapter \",\n                                                currentChapterIndex + 1,\n                                                \" of \",\n                                                chapters.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-muted rounded-full h-2 max-w-xs mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-primary h-2 rounded-full transition-all\",\n                                                style: {\n                                                    width: \"\".concat((currentChapterIndex + 1) / chapters.length * 100, \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        previousChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(previousChapter.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Previous\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Chapter \",\n                                                                    previousChapter.order\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Previous\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Chapter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Back to Novel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        nextChapter ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/novels/\".concat(novelId, \"/chapters/\").concat(nextChapter.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Chapter \",\n                                                                    nextChapter.order\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"ml-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Next\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Chapter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"ml-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(ChapterReader, \"7PPzi0MNZ+c2C9Y2Ce/VmsEGfBo=\", false, function() {\n    return [\n        _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_6__.useGetChaptersQuery\n    ];\n});\n_c = ChapterReader;\nvar _c;\n$RefreshReg$(_c, \"ChapterReader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chapter/chapter-reader.tsx\n"));

/***/ })

});