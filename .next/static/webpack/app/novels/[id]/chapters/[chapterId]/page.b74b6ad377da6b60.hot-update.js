"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/chapters/[chapterId]/page",{

/***/ "(app-pages-browser)/./src/components/chapter/chapter-reader.tsx":
/*!***************************************************!*\
  !*** ./src/components/chapter/chapter-reader.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChapterReader: function() { return /* binding */ ChapterReader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,ChevronLeft,ChevronRight,Palette,Settings,Type!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ChapterReader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ChapterReader(param) {\n    let { chapter, novelId } = param;\n    var _chapter_novel;\n    _s();\n    const [fontSize, setFontSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load reading preferences from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedFontSize = localStorage.getItem(\"reader-font-size\");\n        const savedTheme = localStorage.getItem(\"reader-theme\");\n        if (savedFontSize) setFontSize(parseInt(savedFontSize));\n        if (savedTheme) setTheme(savedTheme);\n    }, []);\n    // Save preferences to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"reader-font-size\", fontSize.toString());\n        localStorage.setItem(\"reader-theme\", theme);\n    }, [\n        fontSize,\n        theme\n    ]);\n    const themeClasses = {\n        light: \"bg-white text-gray-900\",\n        dark: \"bg-gray-900 text-gray-100\",\n        sepia: \"bg-amber-50 text-amber-900\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen transition-colors \".concat(themeClasses[theme]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/novels/\".concat(novelId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Novel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-semibold truncate max-w-md\",\n                                                children: ((_chapter_novel = chapter.novel) === null || _chapter_novel === void 0 ? void 0 : _chapter_novel.title) || \"Novel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Chapter \",\n                                                    chapter.order,\n                                                    \": \",\n                                                    chapter.title\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowSettings(!showSettings),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-muted/50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Font Size:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.max(12, fontSize - 2)),\n                                                disabled: fontSize <= 12,\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-8 text-center text-sm\",\n                                                children: fontSize\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setFontSize(Math.min(24, fontSize + 2)),\n                                                disabled: fontSize >= 24,\n                                                children: \"+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Theme:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            \"light\",\n                                            \"dark\",\n                                            \"sepia\"\n                                        ].map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: theme === t ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setTheme(t),\n                                                className: \"capitalize\",\n                                                children: t\n                                            }, t, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8 max-w-4xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"text-center space-y-4 pb-8 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"mb-2\",\n                                        children: [\n                                            \"Chapter \",\n                                            chapter.order\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl md:text-4xl font-bold tracking-tight\",\n                                        children: chapter.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            \"Published \",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(chapter.createdAt)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose-reading custom-scrollbar\",\n                            style: {\n                                fontSize: \"\".concat(fontSize, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap leading-relaxed\",\n                                dangerouslySetInnerHTML: {\n                                    __html: chapter.content\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"pt-8 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        disabled: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Previous Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/novels/\".concat(novelId),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Back to Novel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        disabled: true,\n                                        children: [\n                                            \"Next Chapter\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_ChevronLeft_ChevronRight_Palette_Settings_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"ml-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/chapter/chapter-reader.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(ChapterReader, \"LLGTOMy2BMPSDy5XrbQrbjXz6N0=\");\n_c = ChapterReader;\nvar _c;\n$RefreshReg$(_c, \"ChapterReader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chapter/chapter-reader.tsx\n"));

/***/ })

});