"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"0a26202dda9a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjVjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBhMjYyMDJkZGE5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/store/api/libraryApi.ts":
/*!*************************************!*\
  !*** ./src/store/api/libraryApi.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   libraryApi: function() { return /* binding */ libraryApi; },\n/* harmony export */   useAddToLibraryMutation: function() { return /* binding */ useAddToLibraryMutation; },\n/* harmony export */   useCheckInLibraryQuery: function() { return /* binding */ useCheckInLibraryQuery; },\n/* harmony export */   useGetLibraryQuery: function() { return /* binding */ useGetLibraryQuery; },\n/* harmony export */   useRemoveFromLibraryMutation: function() { return /* binding */ useRemoveFromLibraryMutation; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst libraryApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    reducerPath: \"libraryApi\",\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: \"/api/library\",\n        credentials: \"include\",\n        prepareHeaders: (headers)=>{\n            headers.set(\"Content-Type\", \"application/json\");\n            return headers;\n        }\n    }),\n    tagTypes: [\n        \"Library\"\n    ],\n    endpoints: (builder)=>({\n            getLibrary: builder.query({\n                query: ()=>\"\",\n                providesTags: [\n                    \"Library\"\n                ]\n            }),\n            addToLibrary: builder.mutation({\n                query: (novelId)=>({\n                        url: \"\",\n                        method: \"POST\",\n                        body: {\n                            novelId\n                        }\n                    }),\n                invalidatesTags: [\n                    \"Library\"\n                ]\n            }),\n            removeFromLibrary: builder.mutation({\n                query: (novelId)=>({\n                        url: \"/\".concat(novelId),\n                        method: \"DELETE\"\n                    }),\n                invalidatesTags: [\n                    \"Library\"\n                ]\n            }),\n            checkInLibrary: builder.query({\n                query: (novelId)=>\"/check/\".concat(novelId),\n                providesTags: (result, error, novelId)=>[\n                        {\n                            type: \"Library\",\n                            id: novelId\n                        }\n                    ]\n            })\n        })\n});\nconst { useGetLibraryQuery, useAddToLibraryMutation, useRemoveFromLibraryMutation, useCheckInLibraryQuery } = libraryApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/api/libraryApi.ts\n"));

/***/ })

});