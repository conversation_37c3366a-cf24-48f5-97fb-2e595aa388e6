"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/browse/page",{

/***/ "(app-pages-browser)/./src/store/api/novelsApi.ts":
/*!************************************!*\
  !*** ./src/store/api/novelsApi.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   novelsApi: function() { return /* binding */ novelsApi; },\n/* harmony export */   useCreateNovelMutation: function() { return /* binding */ useCreateNovelMutation; },\n/* harmony export */   useDeleteNovelMutation: function() { return /* binding */ useDeleteNovelMutation; },\n/* harmony export */   useGetAuthorNovelsQuery: function() { return /* binding */ useGetAuthorNovelsQuery; },\n/* harmony export */   useGetFeaturedNovelsQuery: function() { return /* binding */ useGetFeaturedNovelsQuery; },\n/* harmony export */   useGetNovelQuery: function() { return /* binding */ useGetNovelQuery; },\n/* harmony export */   useGetNovelsQuery: function() { return /* binding */ useGetNovelsQuery; },\n/* harmony export */   usePublishNovelMutation: function() { return /* binding */ usePublishNovelMutation; },\n/* harmony export */   useUpdateNovelMutation: function() { return /* binding */ useUpdateNovelMutation; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst novelsApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    reducerPath: \"novelsApi\",\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: \"/api/novels\"\n    }),\n    tagTypes: [\n        \"Novel\",\n        \"AuthorNovels\"\n    ],\n    endpoints: (builder)=>({\n            // Public endpoints\n            getNovels: builder.query({\n                query: (params)=>({\n                        url: \"\",\n                        params\n                    }),\n                providesTags: [\n                    \"Novel\"\n                ]\n            }),\n            getNovel: builder.query({\n                query: (id)=>\"/\".concat(id),\n                providesTags: (result, error, id)=>[\n                        {\n                            type: \"Novel\",\n                            id\n                        }\n                    ]\n            }),\n            getFeaturedNovels: builder.query({\n                query: ()=>\"/featured\",\n                providesTags: [\n                    \"Novel\"\n                ]\n            }),\n            // Author endpoints\n            getAuthorNovels: builder.query({\n                query: function() {\n                    let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                    return {\n                        url: \"/author\",\n                        params\n                    };\n                },\n                providesTags: [\n                    \"AuthorNovels\"\n                ]\n            }),\n            createNovel: builder.mutation({\n                query: (novel)=>({\n                        url: \"\",\n                        method: \"POST\",\n                        body: novel\n                    }),\n                invalidatesTags: [\n                    \"AuthorNovels\"\n                ]\n            }),\n            updateNovel: builder.mutation({\n                query: (param)=>{\n                    let { id, data } = param;\n                    return {\n                        url: \"/\".concat(id),\n                        method: \"PUT\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Novel\",\n                            id\n                        },\n                        \"AuthorNovels\"\n                    ];\n                }\n            }),\n            deleteNovel: builder.mutation({\n                query: (id)=>({\n                        url: \"/\".concat(id),\n                        method: \"DELETE\"\n                    }),\n                invalidatesTags: [\n                    \"AuthorNovels\"\n                ]\n            }),\n            publishNovel: builder.mutation({\n                query: (param)=>{\n                    let { id, status } = param;\n                    return {\n                        url: \"/\".concat(id, \"/publish\"),\n                        method: \"PUT\",\n                        body: {\n                            status\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Novel\",\n                            id\n                        },\n                        \"AuthorNovels\",\n                        \"Novel\"\n                    ];\n                }\n            })\n        })\n});\nconst { useGetNovelsQuery, useGetNovelQuery, useGetFeaturedNovelsQuery, useGetAuthorNovelsQuery, useCreateNovelMutation, useUpdateNovelMutation, useDeleteNovelMutation, usePublishNovelMutation } = novelsApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/api/novelsApi.ts\n"));

/***/ })

});