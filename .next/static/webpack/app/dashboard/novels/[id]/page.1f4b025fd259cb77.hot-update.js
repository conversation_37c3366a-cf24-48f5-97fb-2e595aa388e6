"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/components/novel/novel-management.tsx":
/*!***************************************************!*\
  !*** ./src/components/novel/novel-management.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NovelManagement: function() { return /* binding */ NovelManagement; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/common/loading-spinner */ \"(app-pages-browser)/./src/components/common/loading-spinner.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* __next_internal_client_entry_do_not_use__ NovelManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NovelManagement(param) {\n    let { novel } = param;\n    var _novel__count, _novel_chapters, _novel__count1, _novel_chapters1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdatingStatus, setIsUpdatingStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishChapter] = (0,_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__.usePublishChapterMutation)();\n    const handlePublishChapter = async (chapterId, currentStatus)=>{\n        try {\n            const newStatus = currentStatus === \"PUBLISHED\" ? \"DRAFT\" : \"PUBLISHED\";\n            await publishChapter({\n                id: chapterId,\n                status: newStatus\n            }).unwrap();\n            toast({\n                title: \"Success\",\n                description: \"Chapter \".concat(newStatus === \"PUBLISHED\" ? \"published\" : \"unpublished\", \" successfully\")\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to \".concat(currentStatus === \"PUBLISHED\" ? \"unpublish\" : \"publish\", \" chapter\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDelete = async ()=>{\n        try {\n            setIsDeleting(true);\n            // Call the API to delete novel\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete novel\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel deleted successfully\"\n            });\n            // Redirect to dashboard\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Error deleting novel:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete novel. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleStatusChange = async (newStatus)=>{\n        try {\n            setIsUpdatingStatus(true);\n            // Call the API to update novel status\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update novel status\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel \".concat(newStatus.toLowerCase(), \" successfully\")\n            });\n            // Refresh the page to show updated status\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error updating novel status:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update novel status. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdatingStatus(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl\",\n                                                    children: novel.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: novel.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                    children: novel.status.toLowerCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-base\",\n                                            children: novel.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Edit Novel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                                    align: \"end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/novels/\".concat(novel.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"View Novel\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Edit Details\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        novel.status === \"DRAFT\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"PUBLISHED\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Publish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"DRAFT\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Unpublish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialog, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                                        onSelect: (e)=>e.preventDefault(),\n                                                                        className: \"text-destructive focus:text-destructive\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Delete Novel\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogHeader, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTitle, {\n                                                                                    children: \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 232,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogDescription, {\n                                                                                    children: [\n                                                                                        'Are you sure you want to delete \"',\n                                                                                        novel.title,\n                                                                                        '\"? This action cannot be undone. All chapters will also be deleted.'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 233,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 231,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogFooter, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogCancel, {\n                                                                                    children: \"Cancel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogAction, {\n                                                                                    onClick: handleDelete,\n                                                                                    disabled: isDeleting,\n                                                                                    className: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n                                                                                    children: isDeleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__.LoadingSpinner, {\n                                                                                                className: \"mr-2 h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                                lineNumber: 247,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            \"Deleting...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 240,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: ((_novel__count = novel._count) === null || _novel__count === void 0 ? void 0 : _novel__count.chapters) || ((_novel_chapters = novel.chapters) === null || _novel_chapters === void 0 ? void 0 : _novel_chapters.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (((_novel__count1 = novel._count) === null || _novel__count1 === void 0 ? void 0 : _novel__count1.chapters) || ((_novel_chapters1 = novel.chapters) === null || _novel_chapters1 === void 0 ? void 0 : _novel_chapters1.length) || 0) === 1 ? \"Chapter\" : \"Chapters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: novel.genre\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Genre\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Manage your novel chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: !novel.chapters || novel.chapters.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-muted-foreground opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"No chapters yet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Start writing by adding your first chapter\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add First Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...novel.chapters || []\n                            ].sort((a, b)=>a.order - b.order).map((chapter)=>{\n                                var _chapter_status;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Chapter \",\n                                                                chapter.order,\n                                                                \": \",\n                                                                chapter.title\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: chapter.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                            children: ((_chapter_status = chapter.status) === null || _chapter_status === void 0 ? void 0 : _chapter_status.toLowerCase()) || \"draft\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(chapter.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id, \"/edit\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, chapter.id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(NovelManagement, \"fDOl6im/ZBfc0jnHzKZxfka4g6k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__.usePublishChapterMutation\n    ];\n});\n_c = NovelManagement;\nvar _c;\n$RefreshReg$(_c, \"NovelManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/novel/novel-management.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/store/api/chaptersApi.ts":
/*!**************************************!*\
  !*** ./src/store/api/chaptersApi.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chaptersApi: function() { return /* binding */ chaptersApi; },\n/* harmony export */   useCreateChapterMutation: function() { return /* binding */ useCreateChapterMutation; },\n/* harmony export */   useDeleteChapterMutation: function() { return /* binding */ useDeleteChapterMutation; },\n/* harmony export */   useGetChapterQuery: function() { return /* binding */ useGetChapterQuery; },\n/* harmony export */   useGetChaptersQuery: function() { return /* binding */ useGetChaptersQuery; },\n/* harmony export */   usePublishChapterMutation: function() { return /* binding */ usePublishChapterMutation; },\n/* harmony export */   useReorderChaptersMutation: function() { return /* binding */ useReorderChaptersMutation; },\n/* harmony export */   useUpdateChapterMutation: function() { return /* binding */ useUpdateChapterMutation; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst chaptersApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    reducerPath: \"chaptersApi\",\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: \"/api\"\n    }),\n    tagTypes: [\n        \"Chapter\",\n        \"NovelChapters\"\n    ],\n    endpoints: (builder)=>({\n            getChapters: builder.query({\n                query: (novelId)=>\"/novels/\".concat(novelId, \"/chapters\"),\n                providesTags: (result, error, novelId)=>[\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ]\n            }),\n            getChapter: builder.query({\n                query: (id)=>\"/chapters/\".concat(id),\n                providesTags: (result, error, id)=>[\n                        {\n                            type: \"Chapter\",\n                            id\n                        }\n                    ]\n            }),\n            createChapter: builder.mutation({\n                query: (param)=>{\n                    let { novelId, data } = param;\n                    return {\n                        url: \"/novels/\".concat(novelId, \"/chapters\"),\n                        method: \"POST\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { novelId } = param;\n                    return [\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ];\n                }\n            }),\n            updateChapter: builder.mutation({\n                query: (param)=>{\n                    let { id, data } = param;\n                    return {\n                        url: \"/chapters/\".concat(id),\n                        method: \"PUT\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Chapter\",\n                            id\n                        }\n                    ];\n                }\n            }),\n            deleteChapter: builder.mutation({\n                query: (id)=>({\n                        url: \"/chapters/\".concat(id),\n                        method: \"DELETE\"\n                    }),\n                invalidatesTags: [\n                    \"NovelChapters\"\n                ]\n            }),\n            reorderChapters: builder.mutation({\n                query: (param)=>{\n                    let { novelId, chapters } = param;\n                    return {\n                        url: \"/novels/\".concat(novelId, \"/chapters/reorder\"),\n                        method: \"PUT\",\n                        body: {\n                            chapters\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { novelId } = param;\n                    return [\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ];\n                }\n            }),\n            publishChapter: builder.mutation({\n                query: (param)=>{\n                    let { id, status } = param;\n                    return {\n                        url: \"/chapters/\".concat(id, \"/publish\"),\n                        method: \"PATCH\",\n                        body: {\n                            status\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Chapter\",\n                            id\n                        },\n                        \"NovelChapters\"\n                    ];\n                }\n            })\n        })\n});\nconst { useGetChaptersQuery, useGetChapterQuery, useCreateChapterMutation, useUpdateChapterMutation, useDeleteChapterMutation, useReorderChaptersMutation, usePublishChapterMutation } = chaptersApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/api/chaptersApi.ts\n"));

/***/ })

});