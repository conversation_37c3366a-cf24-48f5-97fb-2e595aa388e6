"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/novels/[id]/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/archive.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Archive; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.395.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Archive = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Archive\", [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"5\",\n            x: \"2\",\n            y: \"3\",\n            rx: \"1\",\n            key: \"1wp1u1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8\",\n            key: \"1s80jp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 12h4\",\n            key: \"a56b0p\"\n        }\n    ]\n]);\n //# sourceMappingURL=archive.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/send.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Send; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.395.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Send = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Send\", [\n    [\n        \"path\",\n        {\n            d: \"m22 2-7 20-4-9-9-4Z\",\n            key: \"1q3vgg\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 2 11 13\",\n            key: \"nzbqef\"\n        }\n    ]\n]);\n //# sourceMappingURL=send.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLE1BQUFBLE9BQU9DLGdFQUFnQkEsQ0FBQyxRQUFRO0lBQ3BDO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQXVCQyxLQUFLO1FBQUE7S0FBVTtJQUNwRDtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFlQyxLQUFLO1FBQUE7S0FBVTtDQUM3QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL3NlbmQudHM/ZTA2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFNlbmRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1qSWdNaTAzSURJd0xUUXRPUzA1TFRSYUlpQXZQZ29nSUR4d1lYUm9JR1E5SWsweU1pQXlJREV4SURFeklpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3NlbmRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBTZW5kID0gY3JlYXRlTHVjaWRlSWNvbignU2VuZCcsIFtcbiAgWydwYXRoJywgeyBkOiAnbTIyIDItNyAyMC00LTktOS00WicsIGtleTogJzFxM3ZnZycgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00yMiAyIDExIDEzJywga2V5OiAnbnpicWVmJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBTZW5kO1xuIl0sIm5hbWVzIjpbIlNlbmQiLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/novel/novel-management.tsx":
/*!***************************************************!*\
  !*** ./src/components/novel/novel-management.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NovelManagement: function() { return /* binding */ NovelManagement; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/common/loading-spinner */ \"(app-pages-browser)/./src/components/common/loading-spinner.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Send,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* __next_internal_client_entry_do_not_use__ NovelManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NovelManagement(param) {\n    let { novel } = param;\n    var _novel__count, _novel_chapters, _novel__count1, _novel_chapters1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdatingStatus, setIsUpdatingStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishChapter] = (0,_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__.usePublishChapterMutation)();\n    const handlePublishChapter = async (chapterId, currentStatus)=>{\n        try {\n            const newStatus = currentStatus === \"PUBLISHED\" ? \"DRAFT\" : \"PUBLISHED\";\n            await publishChapter({\n                id: chapterId,\n                status: newStatus\n            }).unwrap();\n            toast({\n                title: \"Success\",\n                description: \"Chapter \".concat(newStatus === \"PUBLISHED\" ? \"published\" : \"unpublished\", \" successfully\")\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to \".concat(currentStatus === \"PUBLISHED\" ? \"unpublish\" : \"publish\", \" chapter\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDelete = async ()=>{\n        try {\n            setIsDeleting(true);\n            // Call the API to delete novel\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete novel\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel deleted successfully\"\n            });\n            // Redirect to dashboard\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Error deleting novel:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete novel. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleStatusChange = async (newStatus)=>{\n        try {\n            setIsUpdatingStatus(true);\n            // Call the API to update novel status\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update novel status\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel \".concat(newStatus.toLowerCase(), \" successfully\")\n            });\n            // Refresh the page to show updated status\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error updating novel status:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update novel status. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdatingStatus(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl\",\n                                                    children: novel.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: novel.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                    children: novel.status.toLowerCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-base\",\n                                            children: novel.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Edit Novel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                                    align: \"end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/novels/\".concat(novel.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"View Novel\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Edit Details\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        novel.status === \"DRAFT\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"PUBLISHED\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Publish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"DRAFT\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Unpublish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialog, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                                        onSelect: (e)=>e.preventDefault(),\n                                                                        className: \"text-destructive focus:text-destructive\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Delete Novel\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogHeader, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTitle, {\n                                                                                    children: \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 232,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogDescription, {\n                                                                                    children: [\n                                                                                        'Are you sure you want to delete \"',\n                                                                                        novel.title,\n                                                                                        '\"? This action cannot be undone. All chapters will also be deleted.'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 233,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 231,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogFooter, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogCancel, {\n                                                                                    children: \"Cancel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogAction, {\n                                                                                    onClick: handleDelete,\n                                                                                    disabled: isDeleting,\n                                                                                    className: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n                                                                                    children: isDeleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__.LoadingSpinner, {\n                                                                                                className: \"mr-2 h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                                lineNumber: 247,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            \"Deleting...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 240,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: ((_novel__count = novel._count) === null || _novel__count === void 0 ? void 0 : _novel__count.chapters) || ((_novel_chapters = novel.chapters) === null || _novel_chapters === void 0 ? void 0 : _novel_chapters.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (((_novel__count1 = novel._count) === null || _novel__count1 === void 0 ? void 0 : _novel__count1.chapters) || ((_novel_chapters1 = novel.chapters) === null || _novel_chapters1 === void 0 ? void 0 : _novel_chapters1.length) || 0) === 1 ? \"Chapter\" : \"Chapters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: novel.genre\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Genre\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Manage your novel chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: !novel.chapters || novel.chapters.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-muted-foreground opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"No chapters yet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Start writing by adding your first chapter\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add First Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...novel.chapters || []\n                            ].sort((a, b)=>a.order - b.order).map((chapter)=>{\n                                var _chapter_status;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Chapter \",\n                                                                chapter.order,\n                                                                \": \",\n                                                                chapter.title\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: chapter.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                            children: ((_chapter_status = chapter.status) === null || _chapter_status === void 0 ? void 0 : _chapter_status.toLowerCase()) || \"draft\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(chapter.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: chapter.status === \"PUBLISHED\" ? \"secondary\" : \"default\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>handlePublishChapter(chapter.id, chapter.status),\n                                                    children: chapter.status === \"PUBLISHED\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id, \"/edit\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, chapter.id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(NovelManagement, \"fDOl6im/ZBfc0jnHzKZxfka4g6k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_12__.usePublishChapterMutation\n    ];\n});\n_c = NovelManagement;\nvar _c;\n$RefreshReg$(_c, \"NovelManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/novel/novel-management.tsx\n"));

/***/ })

});