"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/store/api/chaptersApi.ts":
/*!**************************************!*\
  !*** ./src/store/api/chaptersApi.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chaptersApi: function() { return /* binding */ chaptersApi; },\n/* harmony export */   useCreateChapterMutation: function() { return /* binding */ useCreateChapterMutation; },\n/* harmony export */   useDeleteChapterMutation: function() { return /* binding */ useDeleteChapterMutation; },\n/* harmony export */   useGetChapterQuery: function() { return /* binding */ useGetChapterQuery; },\n/* harmony export */   useGetChaptersQuery: function() { return /* binding */ useGetChaptersQuery; },\n/* harmony export */   usePublishChapterMutation: function() { return /* binding */ usePublishChapterMutation; },\n/* harmony export */   useReorderChaptersMutation: function() { return /* binding */ useReorderChaptersMutation; },\n/* harmony export */   useUpdateChapterMutation: function() { return /* binding */ useUpdateChapterMutation; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst chaptersApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    reducerPath: \"chaptersApi\",\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: \"/api\",\n        credentials: \"include\",\n        prepareHeaders: (headers)=>{\n            headers.set(\"Content-Type\", \"application/json\");\n            return headers;\n        }\n    }),\n    tagTypes: [\n        \"Chapter\",\n        \"NovelChapters\"\n    ],\n    endpoints: (builder)=>({\n            getChapters: builder.query({\n                query: (novelId)=>\"/novels/\".concat(novelId, \"/chapters\"),\n                providesTags: (result, error, novelId)=>[\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ]\n            }),\n            getChapter: builder.query({\n                query: (id)=>\"/chapters/\".concat(id),\n                providesTags: (result, error, id)=>[\n                        {\n                            type: \"Chapter\",\n                            id\n                        }\n                    ]\n            }),\n            createChapter: builder.mutation({\n                query: (param)=>{\n                    let { novelId, data } = param;\n                    return {\n                        url: \"/novels/\".concat(novelId, \"/chapters\"),\n                        method: \"POST\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { novelId } = param;\n                    return [\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ];\n                }\n            }),\n            updateChapter: builder.mutation({\n                query: (param)=>{\n                    let { id, data } = param;\n                    return {\n                        url: \"/chapters/\".concat(id),\n                        method: \"PUT\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Chapter\",\n                            id\n                        }\n                    ];\n                }\n            }),\n            deleteChapter: builder.mutation({\n                query: (id)=>({\n                        url: \"/chapters/\".concat(id),\n                        method: \"DELETE\"\n                    }),\n                invalidatesTags: [\n                    \"NovelChapters\"\n                ]\n            }),\n            reorderChapters: builder.mutation({\n                query: (param)=>{\n                    let { novelId, chapters } = param;\n                    return {\n                        url: \"/novels/\".concat(novelId, \"/chapters/reorder\"),\n                        method: \"PUT\",\n                        body: {\n                            chapters\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { novelId } = param;\n                    return [\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ];\n                }\n            }),\n            publishChapter: builder.mutation({\n                query: (param)=>{\n                    let { id, status } = param;\n                    return {\n                        url: \"/chapters/\".concat(id, \"/publish\"),\n                        method: \"PATCH\",\n                        body: {\n                            status\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Chapter\",\n                            id\n                        },\n                        \"NovelChapters\"\n                    ];\n                }\n            })\n        })\n});\nconst { useGetChaptersQuery, useGetChapterQuery, useCreateChapterMutation, useUpdateChapterMutation, useDeleteChapterMutation, useReorderChaptersMutation, usePublishChapterMutation } = chaptersApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/api/chaptersApi.ts\n"));

/***/ })

});