"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/novels/[id]/chapters/new/page",{

/***/ "(app-pages-browser)/./src/store/api/chaptersApi.ts":
/*!**************************************!*\
  !*** ./src/store/api/chaptersApi.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chaptersApi: function() { return /* binding */ chaptersApi; },\n/* harmony export */   useCreateChapterMutation: function() { return /* binding */ useCreateChapterMutation; },\n/* harmony export */   useDeleteChapterMutation: function() { return /* binding */ useDeleteChapterMutation; },\n/* harmony export */   useGetChapterQuery: function() { return /* binding */ useGetChapterQuery; },\n/* harmony export */   useGetChaptersQuery: function() { return /* binding */ useGetChaptersQuery; },\n/* harmony export */   usePublishChapterMutation: function() { return /* binding */ usePublishChapterMutation; },\n/* harmony export */   useReorderChaptersMutation: function() { return /* binding */ useReorderChaptersMutation; },\n/* harmony export */   useUpdateChapterMutation: function() { return /* binding */ useUpdateChapterMutation; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst chaptersApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    reducerPath: \"chaptersApi\",\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: \"/api\",\n        credentials: \"include\",\n        prepareHeaders: (headers)=>{\n            headers.set(\"Content-Type\", \"application/json\");\n            return headers;\n        }\n    }),\n    tagTypes: [\n        \"Chapter\",\n        \"NovelChapters\"\n    ],\n    endpoints: (builder)=>({\n            getChapters: builder.query({\n                query: (novelId)=>\"/novels/\".concat(novelId, \"/chapters\"),\n                providesTags: (result, error, novelId)=>[\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ]\n            }),\n            getChapter: builder.query({\n                query: (id)=>\"/chapters/\".concat(id),\n                providesTags: (result, error, id)=>[\n                        {\n                            type: \"Chapter\",\n                            id\n                        }\n                    ]\n            }),\n            createChapter: builder.mutation({\n                query: (param)=>{\n                    let { novelId, data } = param;\n                    return {\n                        url: \"/novels/\".concat(novelId, \"/chapters\"),\n                        method: \"POST\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { novelId } = param;\n                    return [\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ];\n                }\n            }),\n            updateChapter: builder.mutation({\n                query: (param)=>{\n                    let { id, data } = param;\n                    return {\n                        url: \"/chapters/\".concat(id),\n                        method: \"PUT\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Chapter\",\n                            id\n                        }\n                    ];\n                }\n            }),\n            deleteChapter: builder.mutation({\n                query: (id)=>({\n                        url: \"/chapters/\".concat(id),\n                        method: \"DELETE\"\n                    }),\n                invalidatesTags: [\n                    \"NovelChapters\"\n                ]\n            }),\n            reorderChapters: builder.mutation({\n                query: (param)=>{\n                    let { novelId, chapters } = param;\n                    return {\n                        url: \"/novels/\".concat(novelId, \"/chapters/reorder\"),\n                        method: \"PUT\",\n                        body: {\n                            chapters\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { novelId } = param;\n                    return [\n                        {\n                            type: \"NovelChapters\",\n                            id: novelId\n                        }\n                    ];\n                }\n            }),\n            publishChapter: builder.mutation({\n                query: (param)=>{\n                    let { id, status } = param;\n                    return {\n                        url: \"/chapters/\".concat(id, \"/publish\"),\n                        method: \"PATCH\",\n                        body: {\n                            status\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Chapter\",\n                            id\n                        },\n                        \"NovelChapters\"\n                    ];\n                }\n            })\n        })\n});\nconst { useGetChaptersQuery, useGetChapterQuery, useCreateChapterMutation, useUpdateChapterMutation, useDeleteChapterMutation, useReorderChaptersMutation, usePublishChapterMutation } = chaptersApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS9hcGkvY2hhcHRlcnNBcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUF3RTtBQWNqRSxNQUFNRSxjQUFjRix1RUFBU0EsQ0FBQztJQUNuQ0csYUFBYTtJQUNiQyxXQUFXSCw0RUFBY0EsQ0FBQztRQUN4QkksU0FBUztRQUNUQyxhQUFhO1FBQ2JDLGdCQUFnQixDQUFDQztZQUNmQSxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCO1lBQzVCLE9BQU9EO1FBQ1Q7SUFDRjtJQUNBRSxVQUFVO1FBQUM7UUFBVztLQUFnQjtJQUN0Q0MsV0FBVyxDQUFDQyxVQUFhO1lBQ3ZCQyxhQUFhRCxRQUFRRSxLQUFLLENBQW9CO2dCQUM1Q0EsT0FBTyxDQUFDQyxVQUFZLFdBQW1CLE9BQVJBLFNBQVE7Z0JBQ3ZDQyxjQUFjLENBQUNDLFFBQVFDLE9BQU9ILFVBQVk7d0JBQ3hDOzRCQUFFSSxNQUFNOzRCQUFpQkMsSUFBSUw7d0JBQVE7cUJBQ3RDO1lBQ0g7WUFFQU0sWUFBWVQsUUFBUUUsS0FBSyxDQUFrQjtnQkFDekNBLE9BQU8sQ0FBQ00sS0FBTyxhQUFnQixPQUFIQTtnQkFDNUJKLGNBQWMsQ0FBQ0MsUUFBUUMsT0FBT0UsS0FBTzt3QkFBQzs0QkFBRUQsTUFBTTs0QkFBV0M7d0JBQUc7cUJBQUU7WUFDaEU7WUFFQUUsZUFBZVYsUUFBUVcsUUFBUSxDQUc1QjtnQkFDRFQsT0FBTzt3QkFBQyxFQUFFQyxPQUFPLEVBQUVTLElBQUksRUFBRTsyQkFBTTt3QkFDN0JDLEtBQUssV0FBbUIsT0FBUlYsU0FBUTt3QkFDeEJXLFFBQVE7d0JBQ1JDLE1BQU1IO29CQUNSOztnQkFDQUksaUJBQWlCLENBQUNYLFFBQVFDO3dCQUFPLEVBQUVILE9BQU8sRUFBRTsyQkFBSzt3QkFDL0M7NEJBQUVJLE1BQU07NEJBQWlCQyxJQUFJTDt3QkFBUTtxQkFDdEM7O1lBQ0g7WUFFQWMsZUFBZWpCLFFBQVFXLFFBQVEsQ0FHNUI7Z0JBQ0RULE9BQU87d0JBQUMsRUFBRU0sRUFBRSxFQUFFSSxJQUFJLEVBQUU7MkJBQU07d0JBQ3hCQyxLQUFLLGFBQWdCLE9BQUhMO3dCQUNsQk0sUUFBUTt3QkFDUkMsTUFBTUg7b0JBQ1I7O2dCQUNBSSxpQkFBaUIsQ0FBQ1gsUUFBUUM7d0JBQU8sRUFBRUUsRUFBRSxFQUFFOzJCQUFLO3dCQUMxQzs0QkFBRUQsTUFBTTs0QkFBV0M7d0JBQUc7cUJBQ3ZCOztZQUNIO1lBRUFVLGVBQWVsQixRQUFRVyxRQUFRLENBQWU7Z0JBQzVDVCxPQUFPLENBQUNNLEtBQVE7d0JBQ2RLLEtBQUssYUFBZ0IsT0FBSEw7d0JBQ2xCTSxRQUFRO29CQUNWO2dCQUNBRSxpQkFBaUI7b0JBQUM7aUJBQWdCO1lBQ3BDO1lBRUFHLGlCQUFpQm5CLFFBQVFXLFFBQVEsQ0FHOUI7Z0JBQ0RULE9BQU87d0JBQUMsRUFBRUMsT0FBTyxFQUFFaUIsUUFBUSxFQUFFOzJCQUFNO3dCQUNqQ1AsS0FBSyxXQUFtQixPQUFSVixTQUFRO3dCQUN4QlcsUUFBUTt3QkFDUkMsTUFBTTs0QkFBRUs7d0JBQVM7b0JBQ25COztnQkFDQUosaUJBQWlCLENBQUNYLFFBQVFDO3dCQUFPLEVBQUVILE9BQU8sRUFBRTsyQkFBSzt3QkFDL0M7NEJBQUVJLE1BQU07NEJBQWlCQyxJQUFJTDt3QkFBUTtxQkFDdEM7O1lBQ0g7WUFFQWtCLGdCQUFnQnJCLFFBQVFXLFFBQVEsQ0FHN0I7Z0JBQ0RULE9BQU87d0JBQUMsRUFBRU0sRUFBRSxFQUFFYyxNQUFNLEVBQUU7MkJBQU07d0JBQzFCVCxLQUFLLGFBQWdCLE9BQUhMLElBQUc7d0JBQ3JCTSxRQUFRO3dCQUNSQyxNQUFNOzRCQUFFTzt3QkFBTztvQkFDakI7O2dCQUNBTixpQkFBaUIsQ0FBQ1gsUUFBUUM7d0JBQU8sRUFBRUUsRUFBRSxFQUFFOzJCQUFLO3dCQUMxQzs0QkFBRUQsTUFBTTs0QkFBV0M7d0JBQUc7d0JBQ3RCO3FCQUNEOztZQUNIO1FBQ0Y7QUFDRixHQUFFO0FBRUssTUFBTSxFQUNYZSxtQkFBbUIsRUFDbkJDLGtCQUFrQixFQUNsQkMsd0JBQXdCLEVBQ3hCQyx3QkFBd0IsRUFDeEJDLHdCQUF3QixFQUN4QkMsMEJBQTBCLEVBQzFCQyx5QkFBeUIsRUFDMUIsR0FBR3ZDLFlBQVciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3N0b3JlL2FwaS9jaGFwdGVyc0FwaS50cz8yOTk4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUFwaSwgZmV0Y2hCYXNlUXVlcnkgfSBmcm9tICdAcmVkdXhqcy90b29sa2l0L3F1ZXJ5L3JlYWN0J1xuaW1wb3J0IHR5cGUgeyBDaGFwdGVyLCBDaGFwdGVyU3RhdHVzIH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3JlYXRlQ2hhcHRlclJlcXVlc3Qge1xuICB0aXRsZTogc3RyaW5nXG4gIGNvbnRlbnQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZUNoYXB0ZXJSZXF1ZXN0IHtcbiAgdGl0bGU/OiBzdHJpbmdcbiAgY29udGVudD86IHN0cmluZ1xuICBzdGF0dXM/OiBDaGFwdGVyU3RhdHVzXG59XG5cbmV4cG9ydCBjb25zdCBjaGFwdGVyc0FwaSA9IGNyZWF0ZUFwaSh7XG4gIHJlZHVjZXJQYXRoOiAnY2hhcHRlcnNBcGknLFxuICBiYXNlUXVlcnk6IGZldGNoQmFzZVF1ZXJ5KHtcbiAgICBiYXNlVXJsOiAnL2FwaScsXG4gICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICBwcmVwYXJlSGVhZGVyczogKGhlYWRlcnMpID0+IHtcbiAgICAgIGhlYWRlcnMuc2V0KCdDb250ZW50LVR5cGUnLCAnYXBwbGljYXRpb24vanNvbicpXG4gICAgICByZXR1cm4gaGVhZGVyc1xuICAgIH0sXG4gIH0pLFxuICB0YWdUeXBlczogWydDaGFwdGVyJywgJ05vdmVsQ2hhcHRlcnMnXSxcbiAgZW5kcG9pbnRzOiAoYnVpbGRlcikgPT4gKHtcbiAgICBnZXRDaGFwdGVyczogYnVpbGRlci5xdWVyeTxDaGFwdGVyW10sIHN0cmluZz4oe1xuICAgICAgcXVlcnk6IChub3ZlbElkKSA9PiBgL25vdmVscy8ke25vdmVsSWR9L2NoYXB0ZXJzYCxcbiAgICAgIHByb3ZpZGVzVGFnczogKHJlc3VsdCwgZXJyb3IsIG5vdmVsSWQpID0+IFtcbiAgICAgICAgeyB0eXBlOiAnTm92ZWxDaGFwdGVycycsIGlkOiBub3ZlbElkIH0sXG4gICAgICBdLFxuICAgIH0pLFxuXG4gICAgZ2V0Q2hhcHRlcjogYnVpbGRlci5xdWVyeTxDaGFwdGVyLCBzdHJpbmc+KHtcbiAgICAgIHF1ZXJ5OiAoaWQpID0+IGAvY2hhcHRlcnMvJHtpZH1gLFxuICAgICAgcHJvdmlkZXNUYWdzOiAocmVzdWx0LCBlcnJvciwgaWQpID0+IFt7IHR5cGU6ICdDaGFwdGVyJywgaWQgfV0sXG4gICAgfSksXG5cbiAgICBjcmVhdGVDaGFwdGVyOiBidWlsZGVyLm11dGF0aW9uPENoYXB0ZXIsIHtcbiAgICAgIG5vdmVsSWQ6IHN0cmluZ1xuICAgICAgZGF0YTogQ3JlYXRlQ2hhcHRlclJlcXVlc3RcbiAgICB9Pih7XG4gICAgICBxdWVyeTogKHsgbm92ZWxJZCwgZGF0YSB9KSA9PiAoe1xuICAgICAgICB1cmw6IGAvbm92ZWxzLyR7bm92ZWxJZH0vY2hhcHRlcnNgLFxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogZGF0YSxcbiAgICAgIH0pLFxuICAgICAgaW52YWxpZGF0ZXNUYWdzOiAocmVzdWx0LCBlcnJvciwgeyBub3ZlbElkIH0pID0+IFtcbiAgICAgICAgeyB0eXBlOiAnTm92ZWxDaGFwdGVycycsIGlkOiBub3ZlbElkIH0sXG4gICAgICBdLFxuICAgIH0pLFxuXG4gICAgdXBkYXRlQ2hhcHRlcjogYnVpbGRlci5tdXRhdGlvbjxDaGFwdGVyLCB7XG4gICAgICBpZDogc3RyaW5nXG4gICAgICBkYXRhOiBVcGRhdGVDaGFwdGVyUmVxdWVzdFxuICAgIH0+KHtcbiAgICAgIHF1ZXJ5OiAoeyBpZCwgZGF0YSB9KSA9PiAoe1xuICAgICAgICB1cmw6IGAvY2hhcHRlcnMvJHtpZH1gLFxuICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICBib2R5OiBkYXRhLFxuICAgICAgfSksXG4gICAgICBpbnZhbGlkYXRlc1RhZ3M6IChyZXN1bHQsIGVycm9yLCB7IGlkIH0pID0+IFtcbiAgICAgICAgeyB0eXBlOiAnQ2hhcHRlcicsIGlkIH0sXG4gICAgICBdLFxuICAgIH0pLFxuXG4gICAgZGVsZXRlQ2hhcHRlcjogYnVpbGRlci5tdXRhdGlvbjx2b2lkLCBzdHJpbmc+KHtcbiAgICAgIHF1ZXJ5OiAoaWQpID0+ICh7XG4gICAgICAgIHVybDogYC9jaGFwdGVycy8ke2lkfWAsXG4gICAgICAgIG1ldGhvZDogJ0RFTEVURScsXG4gICAgICB9KSxcbiAgICAgIGludmFsaWRhdGVzVGFnczogWydOb3ZlbENoYXB0ZXJzJ10sXG4gICAgfSksXG5cbiAgICByZW9yZGVyQ2hhcHRlcnM6IGJ1aWxkZXIubXV0YXRpb248dm9pZCwge1xuICAgICAgbm92ZWxJZDogc3RyaW5nXG4gICAgICBjaGFwdGVyczogeyBpZDogc3RyaW5nOyBvcmRlcjogbnVtYmVyIH1bXVxuICAgIH0+KHtcbiAgICAgIHF1ZXJ5OiAoeyBub3ZlbElkLCBjaGFwdGVycyB9KSA9PiAoe1xuICAgICAgICB1cmw6IGAvbm92ZWxzLyR7bm92ZWxJZH0vY2hhcHRlcnMvcmVvcmRlcmAsXG4gICAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICAgIGJvZHk6IHsgY2hhcHRlcnMgfSxcbiAgICAgIH0pLFxuICAgICAgaW52YWxpZGF0ZXNUYWdzOiAocmVzdWx0LCBlcnJvciwgeyBub3ZlbElkIH0pID0+IFtcbiAgICAgICAgeyB0eXBlOiAnTm92ZWxDaGFwdGVycycsIGlkOiBub3ZlbElkIH0sXG4gICAgICBdLFxuICAgIH0pLFxuXG4gICAgcHVibGlzaENoYXB0ZXI6IGJ1aWxkZXIubXV0YXRpb248Q2hhcHRlciwge1xuICAgICAgaWQ6IHN0cmluZ1xuICAgICAgc3RhdHVzOiBDaGFwdGVyU3RhdHVzXG4gICAgfT4oe1xuICAgICAgcXVlcnk6ICh7IGlkLCBzdGF0dXMgfSkgPT4gKHtcbiAgICAgICAgdXJsOiBgL2NoYXB0ZXJzLyR7aWR9L3B1Ymxpc2hgLFxuICAgICAgICBtZXRob2Q6ICdQQVRDSCcsXG4gICAgICAgIGJvZHk6IHsgc3RhdHVzIH0sXG4gICAgICB9KSxcbiAgICAgIGludmFsaWRhdGVzVGFnczogKHJlc3VsdCwgZXJyb3IsIHsgaWQgfSkgPT4gW1xuICAgICAgICB7IHR5cGU6ICdDaGFwdGVyJywgaWQgfSxcbiAgICAgICAgJ05vdmVsQ2hhcHRlcnMnLFxuICAgICAgXSxcbiAgICB9KSxcbiAgfSksXG59KVxuXG5leHBvcnQgY29uc3Qge1xuICB1c2VHZXRDaGFwdGVyc1F1ZXJ5LFxuICB1c2VHZXRDaGFwdGVyUXVlcnksXG4gIHVzZUNyZWF0ZUNoYXB0ZXJNdXRhdGlvbixcbiAgdXNlVXBkYXRlQ2hhcHRlck11dGF0aW9uLFxuICB1c2VEZWxldGVDaGFwdGVyTXV0YXRpb24sXG4gIHVzZVJlb3JkZXJDaGFwdGVyc011dGF0aW9uLFxuICB1c2VQdWJsaXNoQ2hhcHRlck11dGF0aW9uLFxufSA9IGNoYXB0ZXJzQXBpIl0sIm5hbWVzIjpbImNyZWF0ZUFwaSIsImZldGNoQmFzZVF1ZXJ5IiwiY2hhcHRlcnNBcGkiLCJyZWR1Y2VyUGF0aCIsImJhc2VRdWVyeSIsImJhc2VVcmwiLCJjcmVkZW50aWFscyIsInByZXBhcmVIZWFkZXJzIiwiaGVhZGVycyIsInNldCIsInRhZ1R5cGVzIiwiZW5kcG9pbnRzIiwiYnVpbGRlciIsImdldENoYXB0ZXJzIiwicXVlcnkiLCJub3ZlbElkIiwicHJvdmlkZXNUYWdzIiwicmVzdWx0IiwiZXJyb3IiLCJ0eXBlIiwiaWQiLCJnZXRDaGFwdGVyIiwiY3JlYXRlQ2hhcHRlciIsIm11dGF0aW9uIiwiZGF0YSIsInVybCIsIm1ldGhvZCIsImJvZHkiLCJpbnZhbGlkYXRlc1RhZ3MiLCJ1cGRhdGVDaGFwdGVyIiwiZGVsZXRlQ2hhcHRlciIsInJlb3JkZXJDaGFwdGVycyIsImNoYXB0ZXJzIiwicHVibGlzaENoYXB0ZXIiLCJzdGF0dXMiLCJ1c2VHZXRDaGFwdGVyc1F1ZXJ5IiwidXNlR2V0Q2hhcHRlclF1ZXJ5IiwidXNlQ3JlYXRlQ2hhcHRlck11dGF0aW9uIiwidXNlVXBkYXRlQ2hhcHRlck11dGF0aW9uIiwidXNlRGVsZXRlQ2hhcHRlck11dGF0aW9uIiwidXNlUmVvcmRlckNoYXB0ZXJzTXV0YXRpb24iLCJ1c2VQdWJsaXNoQ2hhcHRlck11dGF0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/api/chaptersApi.ts\n"));

/***/ })

});