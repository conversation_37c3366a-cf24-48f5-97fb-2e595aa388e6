"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/novels/[id]/chapters/new/page",{

/***/ "(app-pages-browser)/./src/store/api/novelsApi.ts":
/*!************************************!*\
  !*** ./src/store/api/novelsApi.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   novelsApi: function() { return /* binding */ novelsApi; },\n/* harmony export */   useCreateNovelMutation: function() { return /* binding */ useCreateNovelMutation; },\n/* harmony export */   useDeleteNovelMutation: function() { return /* binding */ useDeleteNovelMutation; },\n/* harmony export */   useGetAuthorNovelsQuery: function() { return /* binding */ useGetAuthorNovelsQuery; },\n/* harmony export */   useGetFeaturedNovelsQuery: function() { return /* binding */ useGetFeaturedNovelsQuery; },\n/* harmony export */   useGetNovelQuery: function() { return /* binding */ useGetNovelQuery; },\n/* harmony export */   useGetNovelsQuery: function() { return /* binding */ useGetNovelsQuery; },\n/* harmony export */   usePublishNovelMutation: function() { return /* binding */ usePublishNovelMutation; },\n/* harmony export */   useUpdateNovelMutation: function() { return /* binding */ useUpdateNovelMutation; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst novelsApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    reducerPath: \"novelsApi\",\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: \"/api/novels\",\n        credentials: \"include\",\n        prepareHeaders: (headers)=>{\n            headers.set(\"Content-Type\", \"application/json\");\n            return headers;\n        }\n    }),\n    tagTypes: [\n        \"Novel\",\n        \"AuthorNovels\"\n    ],\n    endpoints: (builder)=>({\n            // Public endpoints\n            getNovels: builder.query({\n                query: (params)=>({\n                        url: \"\",\n                        params\n                    }),\n                providesTags: [\n                    \"Novel\"\n                ]\n            }),\n            getNovel: builder.query({\n                query: (id)=>\"/\".concat(id),\n                providesTags: (result, error, id)=>[\n                        {\n                            type: \"Novel\",\n                            id\n                        }\n                    ]\n            }),\n            getFeaturedNovels: builder.query({\n                query: ()=>\"/featured\",\n                providesTags: [\n                    \"Novel\"\n                ]\n            }),\n            // Author endpoints\n            getAuthorNovels: builder.query({\n                query: function() {\n                    let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                    return {\n                        url: \"/author\",\n                        params\n                    };\n                },\n                providesTags: [\n                    \"AuthorNovels\"\n                ]\n            }),\n            createNovel: builder.mutation({\n                query: (novel)=>({\n                        url: \"\",\n                        method: \"POST\",\n                        body: novel\n                    }),\n                invalidatesTags: [\n                    \"AuthorNovels\"\n                ]\n            }),\n            updateNovel: builder.mutation({\n                query: (param)=>{\n                    let { id, data } = param;\n                    return {\n                        url: \"/\".concat(id),\n                        method: \"PUT\",\n                        body: data\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Novel\",\n                            id\n                        },\n                        \"AuthorNovels\"\n                    ];\n                }\n            }),\n            deleteNovel: builder.mutation({\n                query: (id)=>({\n                        url: \"/\".concat(id),\n                        method: \"DELETE\"\n                    }),\n                invalidatesTags: [\n                    \"AuthorNovels\"\n                ]\n            }),\n            publishNovel: builder.mutation({\n                query: (param)=>{\n                    let { id, status } = param;\n                    return {\n                        url: \"/\".concat(id, \"/publish\"),\n                        method: \"PUT\",\n                        body: {\n                            status\n                        }\n                    };\n                },\n                invalidatesTags: (result, error, param)=>{\n                    let { id } = param;\n                    return [\n                        {\n                            type: \"Novel\",\n                            id\n                        },\n                        \"AuthorNovels\",\n                        \"Novel\"\n                    ];\n                }\n            })\n        })\n});\nconst { useGetNovelsQuery, useGetNovelQuery, useGetFeaturedNovelsQuery, useGetAuthorNovelsQuery, useCreateNovelMutation, useUpdateNovelMutation, useDeleteNovelMutation, usePublishNovelMutation } = novelsApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/api/novelsApi.ts\n"));

/***/ })

});