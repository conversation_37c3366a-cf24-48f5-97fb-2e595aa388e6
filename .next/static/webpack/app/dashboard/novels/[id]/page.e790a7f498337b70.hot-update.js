"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/components/novel/novel-management.tsx":
/*!***************************************************!*\
  !*** ./src/components/novel/novel-management.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NovelManagement: function() { return /* binding */ NovelManagement; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/common/loading-spinner */ \"(app-pages-browser)/./src/components/common/loading-spinner.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,FileText,MoreVertical,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ NovelManagement auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NovelManagement(param) {\n    let { novel } = param;\n    var _novel__count, _novel_chapters, _novel__count1, _novel_chapters1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdatingStatus, setIsUpdatingStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleDelete = async ()=>{\n        try {\n            setIsDeleting(true);\n            // Call the API to delete novel\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete novel\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel deleted successfully\"\n            });\n            // Redirect to dashboard\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Error deleting novel:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete novel. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleStatusChange = async (newStatus)=>{\n        try {\n            setIsUpdatingStatus(true);\n            // Call the API to update novel status\n            const response = await fetch(\"/api/novels/\".concat(novel.id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update novel status\");\n            }\n            toast({\n                title: \"Success\",\n                description: \"Novel \".concat(newStatus.toLowerCase(), \" successfully\")\n            });\n            // Refresh the page to show updated status\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Error updating novel status:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update novel status. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpdatingStatus(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-2xl\",\n                                                    children: novel.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: novel.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                    children: novel.status.toLowerCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-base\",\n                                            children: novel.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Edit Novel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuContent, {\n                                                    align: \"end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/novels/\".concat(novel.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"View Novel\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                href: \"/dashboard/novels/\".concat(novel.id, \"/edit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Edit Details\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        novel.status === \"DRAFT\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"PUBLISHED\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Publish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                            onClick: ()=>handleStatusChange(\"DRAFT\"),\n                                                            disabled: isUpdatingStatus,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Unpublish Novel\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuSeparator, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialog, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_9__.DropdownMenuItem, {\n                                                                        onSelect: (e)=>e.preventDefault(),\n                                                                        className: \"text-destructive focus:text-destructive\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            \"Delete Novel\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogHeader, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogTitle, {\n                                                                                    children: \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 213,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogDescription, {\n                                                                                    children: [\n                                                                                        'Are you sure you want to delete \"',\n                                                                                        novel.title,\n                                                                                        '\"? This action cannot be undone. All chapters will also be deleted.'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 214,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 212,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogFooter, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogCancel, {\n                                                                                    children: \"Cancel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 220,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_8__.AlertDialogAction, {\n                                                                                    onClick: handleDelete,\n                                                                                    disabled: isDeleting,\n                                                                                    className: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n                                                                                    children: isDeleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_loading_spinner__WEBPACK_IMPORTED_MODULE_6__.LoadingSpinner, {\n                                                                                                className: \"mr-2 h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                                lineNumber: 228,\n                                                                                                columnNumber: 29\n                                                                                            }, this),\n                                                                                            \"Deleting...\"\n                                                                                        ]\n                                                                                    }, void 0, true) : \"Delete Novel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                                    lineNumber: 221,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: ((_novel__count = novel._count) === null || _novel__count === void 0 ? void 0 : _novel__count.chapters) || ((_novel_chapters = novel.chapters) === null || _novel_chapters === void 0 ? void 0 : _novel_chapters.length) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (((_novel__count1 = novel._count) === null || _novel__count1 === void 0 ? void 0 : _novel__count1.chapters) || ((_novel_chapters1 = novel.chapters) === null || _novel_chapters1 === void 0 ? void 0 : _novel_chapters1.length) || 0) === 1 ? \"Chapter\" : \"Chapters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: novel.genre\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Genre\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(novel.updatedAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Manage your novel chapters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: !novel.chapters || novel.chapters.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto text-muted-foreground opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"No chapters yet\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Start writing by adding your first chapter\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/new\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add First Chapter\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...novel.chapters || []\n                            ].sort((a, b)=>a.order - b.order).map((chapter)=>{\n                                var _chapter_status;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Chapter \",\n                                                                chapter.order,\n                                                                \": \",\n                                                                chapter.title\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: chapter.status === \"PUBLISHED\" ? \"default\" : \"secondary\",\n                                                            children: ((_chapter_status = chapter.status) === null || _chapter_status === void 0 ? void 0 : _chapter_status.toLowerCase()) || \"draft\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(chapter.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        href: \"/dashboard/novels/\".concat(novel.id, \"/chapters/\").concat(chapter.id, \"/edit\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_FileText_MoreVertical_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, chapter.id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/novel/novel-management.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(NovelManagement, \"qOpJsVJ9qi1YEJyCnxKm9ANlh4c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = NovelManagement;\nvar _c;\n$RefreshReg$(_c, \"NovelManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/novel/novel-management.tsx\n"));

/***/ })

});