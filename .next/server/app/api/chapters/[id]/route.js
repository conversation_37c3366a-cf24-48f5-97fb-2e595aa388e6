"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chapters/[id]/route";
exports.ids = ["app/api/chapters/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_chapters_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chapters/[id]/route.ts */ \"(rsc)/./src/app/api/chapters/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chapters/[id]/route\",\n        pathname: \"/api/chapters/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/chapters/[id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/chapters/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_chapters_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chapters/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chapters/[id]/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/chapters/[id]/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_api_middleware__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api-middleware */ \"(rsc)/./src/lib/api-middleware.ts\");\n\n\n\n\n\n\nasync function GET(request, { params }) {\n    const { id } = params;\n    return (0,_lib_api_middleware__WEBPACK_IMPORTED_MODULE_5__.withChapterAccess)(request, async ()=>{\n        try {\n            const chapter = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findUnique({\n                where: {\n                    id\n                },\n                include: {\n                    novel: {\n                        select: {\n                            id: true,\n                            title: true,\n                            authorId: true,\n                            status: true,\n                            isPremium: true,\n                            requiredTier: true\n                        }\n                    }\n                }\n            });\n            if (!chapter) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Chapter not found\"\n                }, {\n                    status: 404\n                });\n            }\n            // Check if user can access this chapter\n            const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n            const isAuthor = session?.user?.id === chapter.novel.authorId;\n            // Only published chapters are accessible to non-authors\n            if (chapter.status !== _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED && !isAuthor) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Chapter not found\"\n                }, {\n                    status: 404\n                });\n            }\n            // Only published novels' chapters are accessible to non-authors\n            if (chapter.novel.status !== \"PUBLISHED\" && !isAuthor) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Chapter not found\"\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(chapter);\n        } catch (error) {\n            console.error(\"Error fetching chapter:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch chapter\"\n            }, {\n                status: 500\n            });\n        }\n    }, id);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chapters/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-middleware.ts":
/*!***********************************!*\
  !*** ./src/lib/api-middleware.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractContentId: () => (/* binding */ extractContentId),\n/* harmony export */   withChapterAccess: () => (/* binding */ withChapterAccess),\n/* harmony export */   withContentAccess: () => (/* binding */ withContentAccess),\n/* harmony export */   withNovelAccess: () => (/* binding */ withNovelAccess)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_content_access__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/content-access */ \"(rsc)/./src/lib/content-access.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n/**\n * Middleware to check content access for API routes\n */ async function withContentAccess(request, handler, options) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const { contentId, contentType, allowAuthor = true } = options;\n        // Get content from database\n        let content;\n        let authorId;\n        if (contentType === \"novel\") {\n            content = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n                where: {\n                    id: contentId\n                },\n                select: {\n                    id: true,\n                    isPremium: true,\n                    requiredTier: true,\n                    authorId: true\n                }\n            });\n            authorId = content?.authorId;\n        } else {\n            content = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findUnique({\n                where: {\n                    id: contentId\n                },\n                include: {\n                    novel: {\n                        select: {\n                            isPremium: true,\n                            requiredTier: true,\n                            authorId: true\n                        }\n                    }\n                }\n            });\n            authorId = content?.novel?.authorId;\n        }\n        if (!content) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Content not found\"\n            }, {\n                status: 404\n            });\n        }\n        // If content is not premium, allow access\n        const isPremium = contentType === \"novel\" ? content.isPremium : content.isPremium || content.novel?.isPremium;\n        if (!isPremium) {\n            return handler(request);\n        }\n        // If user is the author and author access is allowed, grant access\n        if (allowAuthor && session?.user && (0,_lib_content_access__WEBPACK_IMPORTED_MODULE_4__.isContentAuthor)(session.user, authorId)) {\n            return handler(request);\n        }\n        // Check subscription access\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication required\",\n                code: \"AUTH_REQUIRED\",\n                message: \"Please sign in to access premium content\"\n            }, {\n                status: 401\n            });\n        }\n        // Get user with subscription\n        const userWithSubscription = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: session.user.id\n            },\n            include: {\n                subscriptions: {\n                    where: {\n                        status: {\n                            in: [\n                                _prisma_client__WEBPACK_IMPORTED_MODULE_5__.SubscriptionStatus.ACTIVE,\n                                _prisma_client__WEBPACK_IMPORTED_MODULE_5__.SubscriptionStatus.TRIALING\n                            ]\n                        }\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 1\n                }\n            }\n        });\n        if (!userWithSubscription) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check content access\n        const requiredTier = contentType === \"novel\" ? content.requiredTier : content.requiredTier || content.novel?.requiredTier;\n        const accessResult = (0,_lib_content_access__WEBPACK_IMPORTED_MODULE_4__.checkContentAccess)(userWithSubscription, {\n            isPremium,\n            requiredTier\n        });\n        if (!accessResult.hasAccess) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Premium content access required\",\n                code: \"PREMIUM_REQUIRED\",\n                reason: accessResult.reason,\n                requiredTier: accessResult.requiredTier,\n                currentTier: accessResult.currentTier,\n                message: \"This content requires a premium subscription\"\n            }, {\n                status: 403\n            });\n        }\n        // User has access, proceed with the request\n        return handler(request);\n    } catch (error) {\n        console.error(\"Content access middleware error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * Helper function to extract content ID from request URL\n */ function extractContentId(request, paramName = \"id\") {\n    const url = new URL(request.url);\n    const pathSegments = url.pathname.split(\"/\");\n    // Find the segment after the param name or use the last segment\n    const idIndex = pathSegments.findIndex((segment)=>segment === paramName);\n    if (idIndex !== -1 && idIndex + 1 < pathSegments.length) {\n        return pathSegments[idIndex + 1];\n    }\n    // Fallback to last segment if it looks like an ID\n    const lastSegment = pathSegments[pathSegments.length - 1];\n    if (lastSegment && lastSegment.length > 10) {\n        return lastSegment;\n    }\n    return null;\n}\n/**\n * Wrapper for novel content access\n */ async function withNovelAccess(request, handler, novelId, allowAuthor = true) {\n    return withContentAccess(request, handler, {\n        contentId: novelId,\n        contentType: \"novel\",\n        allowAuthor\n    });\n}\n/**\n * Wrapper for chapter content access\n */ async function withChapterAccess(request, handler, chapterId, allowAuthor = true) {\n    return withContentAccess(request, handler, {\n        contentId: chapterId,\n        contentType: \"chapter\",\n        allowAuthor\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-middleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.READER;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/content-access.ts":
/*!***********************************!*\
  !*** ./src/lib/content-access.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkChapterAccess: () => (/* binding */ checkChapterAccess),\n/* harmony export */   checkContentAccess: () => (/* binding */ checkContentAccess),\n/* harmony export */   checkNovelAccess: () => (/* binding */ checkNovelAccess),\n/* harmony export */   getAccessReasonMessage: () => (/* binding */ getAccessReasonMessage),\n/* harmony export */   getTierDisplayName: () => (/* binding */ getTierDisplayName),\n/* harmony export */   isContentAuthor: () => (/* binding */ isContentAuthor)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Check if a user has access to premium content based on their subscription or credit purchases\n */ function checkContentAccess(user, content) {\n    // If content is not premium, everyone has access\n    if (!content.isPremium) {\n        return {\n            hasAccess: true\n        };\n    }\n    // If user is not logged in, no access to premium content\n    if (!user) {\n        return {\n            hasAccess: false,\n            reason: \"no_subscription\",\n            requiredTier: content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM\n        };\n    }\n    // Get user's active subscription\n    const activeSubscription = user.subscriptions.find((sub)=>sub.status === _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionStatus.ACTIVE || sub.status === _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionStatus.TRIALING);\n    // If no active subscription, no access\n    if (!activeSubscription) {\n        return {\n            hasAccess: false,\n            reason: \"no_subscription\",\n            requiredTier: content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM\n        };\n    }\n    // Check if subscription tier is sufficient\n    const requiredTier = content.requiredTier || _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM;\n    const userTier = activeSubscription.tier;\n    // Define tier hierarchy\n    const tierHierarchy = {\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.FREE]: 0,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM]: 1,\n        [_prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM_PLUS]: 2\n    };\n    const userTierLevel = tierHierarchy[userTier];\n    const requiredTierLevel = tierHierarchy[requiredTier];\n    if (userTierLevel < requiredTierLevel) {\n        return {\n            hasAccess: false,\n            reason: \"insufficient_tier\",\n            requiredTier,\n            currentTier: userTier\n        };\n    }\n    return {\n        hasAccess: true,\n        currentTier: userTier\n    };\n}\n/**\n * Check if a user can access a specific novel\n */ function checkNovelAccess(user, novel) {\n    return checkContentAccess(user, {\n        isPremium: novel.isPremium,\n        requiredTier: novel.requiredTier\n    });\n}\n/**\n * Check if a user can access a specific chapter\n */ function checkChapterAccess(user, chapter, novel) {\n    // First check chapter-level restrictions\n    const chapterAccess = checkContentAccess(user, {\n        isPremium: chapter.isPremium,\n        requiredTier: chapter.requiredTier\n    });\n    // If chapter access is denied, return that result\n    if (!chapterAccess.hasAccess) {\n        return chapterAccess;\n    }\n    // If novel is provided, also check novel-level restrictions\n    if (novel) {\n        const novelAccess = checkContentAccess(user, {\n            isPremium: novel.isPremium,\n            requiredTier: novel.requiredTier\n        });\n        if (!novelAccess.hasAccess) {\n            return novelAccess;\n        }\n    }\n    return chapterAccess;\n}\n/**\n * Get the display name for a subscription tier\n */ function getTierDisplayName(tier) {\n    switch(tier){\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.FREE:\n            return \"Free\";\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM:\n            return \"Premium\";\n        case _prisma_client__WEBPACK_IMPORTED_MODULE_0__.SubscriptionTier.PREMIUM_PLUS:\n            return \"Premium Plus\";\n        default:\n            return tier;\n    }\n}\n/**\n * Get the access reason message for display\n */ function getAccessReasonMessage(result) {\n    if (result.hasAccess) {\n        return \"You have access to this content\";\n    }\n    switch(result.reason){\n        case \"no_subscription\":\n            return `This content requires a ${getTierDisplayName(result.requiredTier)} subscription`;\n        case \"insufficient_tier\":\n            return `This content requires ${getTierDisplayName(result.requiredTier)} or higher. You currently have ${getTierDisplayName(result.currentTier)}`;\n        case \"subscription_inactive\":\n            return \"Your subscription is not active. Please check your billing information\";\n        default:\n            return \"You do not have access to this premium content\";\n    }\n}\n/**\n * Check if user is an author of the content (authors can always access their own content)\n */ function isContentAuthor(user, authorId) {\n    return user?.id === authorId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/content-access.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/preact","vendor-chunks/cookie","vendor-chunks/@next-auth"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();