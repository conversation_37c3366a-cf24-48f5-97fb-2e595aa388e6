"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chapters/[id]/publish/route";
exports.ids = ["app/api/chapters/[id]/publish/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_chapters_id_publish_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chapters/[id]/publish/route.ts */ \"(rsc)/./src/app/api/chapters/[id]/publish/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chapters/[id]/publish/route\",\n        pathname: \"/api/chapters/[id]/publish\",\n        filename: \"route\",\n        bundlePath: \"app/api/chapters/[id]/publish/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/chapters/[id]/publish/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_chapters_id_publish_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chapters/[id]/publish/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chapters/[id]/publish/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/chapters/[id]/publish/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function PATCH(request, { params }) {\n    const { id } = params;\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Get chapter and verify ownership\n        const chapter = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findUnique({\n            where: {\n                id\n            },\n            include: {\n                novel: {\n                    select: {\n                        id: true,\n                        title: true,\n                        authorId: true,\n                        status: true\n                    }\n                }\n            }\n        });\n        if (!chapter) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Chapter not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (chapter.novel.authorId !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"You don't have permission to edit this chapter\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { status } = body;\n        // Validate status\n        if (!status || !Object.values(_prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus).includes(status)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid status. Must be DRAFT or PUBLISHED.\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate chapter content before publishing\n        if (status === _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED) {\n            if (!chapter.title.trim() || !chapter.content.trim()) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Cannot publish chapter with empty title or content\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Update chapter status\n        const updatedChapter = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.update({\n            where: {\n                id\n            },\n            data: {\n                status: status,\n                // Update timestamp when publishing\n                ...status === _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED && {\n                    updatedAt: new Date()\n                }\n            },\n            include: {\n                novel: {\n                    select: {\n                        id: true,\n                        title: true,\n                        authorId: true,\n                        status: true\n                    }\n                }\n            }\n        });\n        const actionMessage = status === _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED ? \"Chapter published successfully\" : \"Chapter unpublished successfully\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedChapter,\n            message: actionMessage\n        });\n    } catch (error) {\n        console.error(\"Error updating chapter status:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update chapter status\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Batch update multiple chapters' publish status\nasync function POST(request, { params }) {\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { chapterIds, status } = body;\n        // Validate inputs\n        if (!Array.isArray(chapterIds) || chapterIds.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Chapter IDs array is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (!status || !Object.values(_prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus).includes(status)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid status. Must be DRAFT or PUBLISHED.\"\n            }, {\n                status: 400\n            });\n        }\n        // Verify all chapters belong to the author\n        const chapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findMany({\n            where: {\n                id: {\n                    in: chapterIds\n                }\n            },\n            include: {\n                novel: {\n                    select: {\n                        authorId: true\n                    }\n                }\n            }\n        });\n        // Check if all chapters exist and belong to the author\n        if (chapters.length !== chapterIds.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Some chapters not found\"\n            }, {\n                status: 404\n            });\n        }\n        const unauthorizedChapters = chapters.filter((chapter)=>chapter.novel.authorId !== session.user.id);\n        if (unauthorizedChapters.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"You don't have permission to edit some of these chapters\"\n            }, {\n                status: 403\n            });\n        }\n        // Validate content for publishing\n        if (status === _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED) {\n            const invalidChapters = chapters.filter((chapter)=>!chapter.title.trim() || !chapter.content.trim());\n            if (invalidChapters.length > 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Cannot publish chapters with empty title or content\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Update all chapters\n        const updatedChapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.updateMany({\n            where: {\n                id: {\n                    in: chapterIds\n                }\n            },\n            data: {\n                status: status,\n                ...status === _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED && {\n                    updatedAt: new Date()\n                }\n            }\n        });\n        const actionMessage = status === _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED ? `${updatedChapters.count} chapters published successfully` : `${updatedChapters.count} chapters unpublished successfully`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                updatedCount: updatedChapters.count,\n                status\n            },\n            message: actionMessage\n        });\n    } catch (error) {\n        console.error(\"Error batch updating chapter status:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update chapter status\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chapters/[id]/publish/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || _prisma_client__WEBPACK_IMPORTED_MODULE_3__.UserRole.READER;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();