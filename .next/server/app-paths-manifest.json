{"/novels/[id]/chapters/[chapterId]/page": "app/novels/[id]/chapters/[chapterId]/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/chapters/[id]/route": "app/api/chapters/[id]/route.js", "/api/novels/[id]/chapters/route": "app/api/novels/[id]/chapters/route.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js", "/authors/[id]/page": "app/authors/[id]/page.js", "/api/authors/[id]/route": "app/api/authors/[id]/route.js"}