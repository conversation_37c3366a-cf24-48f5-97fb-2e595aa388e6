{"/dashboard/page": "app/dashboard/page.js", "/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/dashboard/novels/[id]/chapters/[chapterId]/edit/page": "app/dashboard/novels/[id]/chapters/[chapterId]/edit/page.js", "/novels/[id]/chapters/[chapterId]/page": "app/novels/[id]/chapters/[chapterId]/page.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/browse/page": "app/browse/page.js", "/api/novels/route": "app/api/novels/route.js", "/_not-found/page": "app/_not-found/page.js"}