# Credit System Implementation Guide

## 🎯 Overview

The credit system provides an alternative monetization method for writers alongside subscriptions. Readers can purchase credits to unlock individual premium content pieces, while writers earn revenue from each credit-based purchase.

## 💰 How Writers Monetize with Credits

### Revenue Streams
1. **Subscription Revenue** (70% share)
   - Monthly/yearly subscription fees from premium tiers
   - Recurring revenue from active subscribers

2. **Credit Purchases** (70% share)
   - One-time payments for individual content
   - Direct reader-to-writer transactions
   - Flexible pricing per chapter/novel

3. **Direct Tips** (70% share)
   - Reader appreciation payments
   - Bonus income on top of content sales

### Credit System Benefits for Writers
- **Flexible Pricing**: Set custom credit prices for different content
- **Immediate Revenue**: Earn from each content unlock
- **Reader Choice**: Offer alternatives to subscription commitment
- **Higher Engagement**: Readers invest directly in specific content
- **Transparent Earnings**: Real-time tracking of credit-based income

## 🏗️ System Architecture

### Database Schema
```
Users
├── creditBalance (Int) - Current credit balance
├── creditTransactions[] - Transaction history
├── creditPurchases[] - Credit package purchases
└── contentPurchases[] - Content unlocked with credits

CreditPackages
├── credits (Int) - Number of credits
├── bonusCredits (Int) - Bonus credits for bulk purchases
├── price (Decimal) - USD price
└── stripePriceId (String) - Stripe integration

CreditTransactions
├── type (PURCHASE|SPEND|REFUND|BONUS|ADMIN_ADJUSTMENT)
├── amount (Int) - Credit amount (+ or -)
├── balanceBefore/After (Int) - Balance tracking
└── sourceType/sourceId - Transaction source

ContentPurchases
├── contentType (NOVEL|CHAPTER)
├── contentId (String)
├── creditsSpent (Int)
└── accessGranted (Boolean)
```

### API Endpoints
```
GET  /api/credits/balance          - Get user credit balance
GET  /api/credits/transactions     - Get transaction history
GET  /api/credits/packages         - Get available credit packages
POST /api/credits/purchase         - Purchase credit package
POST /api/credits/spend            - Spend credits on content
GET  /api/earnings/summary         - Enhanced earnings with credit metrics
```

## 💳 Credit Packages

### Default Packages
1. **Starter Pack**: 50 credits for $4.99
2. **Value Pack**: 120 credits + 10 bonus for $9.99 (10% bonus)
3. **Premium Pack**: 300 credits + 50 bonus for $19.99 (16% bonus)
4. **Ultimate Pack**: 600 credits + 150 bonus for $34.99 (25% bonus)

### Pricing Strategy
- 1 credit = $0.10 USD equivalent
- Bulk purchases include bonus credits
- Competitive with subscription pricing
- Encourages larger purchases

## 🎨 UI Components

### For Readers
- **CreditBalance**: Shows current balance with top-up button
- **CreditPurchaseModal**: Package selection and purchase flow
- **ContentPaywall**: Credit vs subscription options
- **CreditTransactionHistory**: Purchase and spending history

### For Writers
- **CreditEarningsDashboard**: Revenue analytics and metrics
- **EarningsBreakdown**: Credit vs subscription comparison
- **ContentPricing**: Set credit prices for content

## 🔧 Setup Instructions

### 1. Database Migration
```bash
# Apply schema changes
npx prisma db push

# Run migration script
npx tsx scripts/migrate-credit-system.ts

# Seed credit packages
npx tsx prisma/seeds/credit-packages.ts
```

### 2. Stripe Configuration
```bash
# Create credit package products in Stripe Dashboard
# Update environment variables with price IDs
STRIPE_STARTER_PACK_PRICE_ID="price_xxx"
STRIPE_VALUE_PACK_PRICE_ID="price_xxx"
STRIPE_PREMIUM_PACK_PRICE_ID="price_xxx"
STRIPE_ULTIMATE_PACK_PRICE_ID="price_xxx"
```

### 3. Webhook Setup
```bash
# Add webhook endpoint: /api/webhooks/stripe
# Subscribe to events:
# - payment_intent.succeeded
# - payment_intent.payment_failed
```

### 4. Content Pricing
```typescript
// Set credit prices for content
await prisma.novel.update({
  where: { id: novelId },
  data: { 
    isPremium: true,
    creditPrice: 50 // 50 credits = $5.00
  }
})

await prisma.chapter.update({
  where: { id: chapterId },
  data: { 
    isPremium: true,
    creditPrice: 5 // 5 credits = $0.50
  }
})
```

## 📊 Revenue Sharing

### Split Structure
- **Writer**: 70% of credit purchase value
- **Platform**: 30% for hosting, processing, support

### Payout System
- **Minimum**: $50 accumulated earnings
- **Frequency**: Monthly automated payouts
- **Method**: Stripe Connect transfers
- **Tracking**: Real-time earnings dashboard

### Example Calculation
```
Reader purchases 100 credits for $10.00
├── Platform fee (30%): $3.00
└── Writer earning (70%): $7.00

Reader spends 5 credits on chapter ($0.50 value)
├── Platform fee: $0.15
└── Writer earning: $0.35
```

## 🎯 Content Access Logic

### Access Priority
1. **Free Content**: Always accessible
2. **Subscription Access**: Active subscription grants access
3. **Credit Purchase**: Individual content unlock
4. **Paywall**: Show purchase options

### Implementation
```typescript
const accessInfo = getContentAccessInfo(user, {
  isPremium: chapter.isPremium,
  requiredTier: chapter.requiredTier,
  creditPrice: chapter.creditPrice,
  id: chapter.id,
  contentType: 'CHAPTER'
})

if (!accessInfo.hasAccess) {
  return <ContentPaywall 
    contentType="CHAPTER"
    contentId={chapter.id}
    accessInfo={accessInfo}
    onAccessGranted={() => window.location.reload()}
  />
}
```

## 📈 Analytics & Metrics

### Writer Dashboard Metrics
- Total credit revenue
- Credit transactions count
- Average transaction value
- Credit vs subscription earnings
- Monthly trends
- Unpaid balance

### Platform Metrics
- Total credits sold
- Revenue by package type
- User engagement with credits
- Conversion rates
- Popular content pricing

## 🔒 Security Considerations

### Payment Security
- Stripe handles all payment processing
- PCI compliance through Stripe
- Webhook signature verification
- Idempotent transaction processing

### Credit Security
- Server-side balance validation
- Transaction atomicity
- Audit trail for all changes
- Rate limiting on purchases

### Content Access
- Server-side access validation
- Secure content delivery
- Purchase verification
- Expiration handling

## 🚀 Future Enhancements

### Planned Features
- **Credit Gifting**: Send credits to other users
- **Bulk Discounts**: Volume pricing for content
- **Subscription Credits**: Monthly credit allowances
- **Referral Bonuses**: Earn credits for referrals
- **Seasonal Promotions**: Limited-time bonus offers

### Advanced Analytics
- Revenue forecasting
- Price optimization suggestions
- Reader behavior analysis
- Content performance metrics
- A/B testing for pricing

## 🆘 Troubleshooting

### Common Issues
1. **Credit Balance Not Updating**: Check webhook processing
2. **Purchase Failures**: Verify Stripe configuration
3. **Access Denied**: Validate content purchase records
4. **Payout Issues**: Confirm Stripe Connect setup

### Debug Commands
```bash
# Check credit transactions
npx prisma studio

# Test webhook locally
stripe listen --forward-to localhost:3000/api/webhooks/stripe

# Verify credit package creation
curl -X GET http://localhost:3000/api/credits/packages
```

## 📞 Support

For implementation questions or issues:
1. Check the troubleshooting section
2. Review API endpoint documentation
3. Test with Stripe test mode
4. Verify database schema changes
5. Contact development team for assistance
