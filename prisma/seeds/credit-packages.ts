import { PrismaClient } from '@prisma/client'
import { DEFAULT_CREDIT_PACKAGES } from '../../src/lib/credits'

const prisma = new PrismaClient()

export async function seedCreditPackages() {
  console.log('🌱 Seeding credit packages...')

  // Clear existing packages
  await prisma.creditPackage.deleteMany()

  // Create default credit packages
  for (const packageData of DEFAULT_CREDIT_PACKAGES) {
    await prisma.creditPackage.create({
      data: {
        ...packageData,
        isActive: true,
      }
    })
  }

  console.log(`✅ Created ${DEFAULT_CREDIT_PACKAGES.length} credit packages`)
}

// Run if called directly
if (require.main === module) {
  seedCreditPackages()
    .catch((e) => {
      console.error(e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}
