#!/usr/bin/env tsx

/**
 * Migration script to set up the credit system
 * This script should be run after updating the Prisma schema
 */

import { PrismaClient } from '@prisma/client'
import { DEFAULT_CREDIT_PACKAGES } from '../src/lib/credits'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 Starting credit system migration...')

  try {
    // 1. Apply database schema changes
    console.log('📊 Applying database schema changes...')
    console.log('   Run: npx prisma db push')
    console.log('   Or: npx prisma migrate dev --name add-credit-system')

    // 2. Seed credit packages
    console.log('🌱 Seeding credit packages...')
    
    // Clear existing packages
    await prisma.creditPackage.deleteMany()
    console.log('   Cleared existing credit packages')

    // Create default credit packages
    for (const packageData of DEFAULT_CREDIT_PACKAGES) {
      const creditPackage = await prisma.creditPackage.create({
        data: {
          ...packageData,
          isActive: true,
        }
      })
      console.log(`   ✅ Created package: ${creditPackage.name} (${creditPackage.credits} credits for $${creditPackage.price})`)
    }

    // 3. Update existing users with default credit balance (0)
    console.log('👥 Updating existing users...')
    const userUpdateResult = await prisma.user.updateMany({
      where: {
        creditBalance: {
          equals: null
        }
      },
      data: {
        creditBalance: 0
      }
    })
    console.log(`   ✅ Updated ${userUpdateResult.count} users with default credit balance`)

    // 4. Set default credit prices for existing content (optional)
    console.log('📚 Setting default credit prices for existing content...')
    
    // Update novels without credit prices
    const novelUpdateResult = await prisma.novel.updateMany({
      where: {
        isPremium: true,
        creditPrice: null
      },
      data: {
        creditPrice: 50 // Default 50 credits for novels
      }
    })
    console.log(`   ✅ Updated ${novelUpdateResult.count} novels with default credit prices`)

    // Update chapters without credit prices
    const chapterUpdateResult = await prisma.chapter.updateMany({
      where: {
        isPremium: true,
        creditPrice: null
      },
      data: {
        creditPrice: 5 // Default 5 credits for chapters
      }
    })
    console.log(`   ✅ Updated ${chapterUpdateResult.count} chapters with default credit prices`)

    // 5. Create sample credit transactions for testing (optional)
    if (process.env.NODE_ENV === 'development') {
      console.log('🧪 Creating sample data for development...')
      
      // Find first author user
      const author = await prisma.user.findFirst({
        where: { role: 'AUTHOR' }
      })

      if (author) {
        // Give author some credits for testing
        await prisma.user.update({
          where: { id: author.id },
          data: { creditBalance: 100 }
        })

        // Create a sample credit transaction
        await prisma.creditTransaction.create({
          data: {
            userId: author.id,
            type: 'BONUS',
            status: 'COMPLETED',
            amount: 100,
            description: 'Welcome bonus credits',
            sourceType: 'bonus',
            sourceId: 'welcome-bonus',
            balanceBefore: 0,
            balanceAfter: 100,
          }
        })
        console.log(`   ✅ Gave ${author.name || author.email} 100 bonus credits for testing`)
      }
    }

    console.log('✨ Credit system migration completed successfully!')
    console.log('')
    console.log('📋 Next steps:')
    console.log('1. Update your Stripe dashboard with credit package price IDs')
    console.log('2. Test credit purchases in development')
    console.log('3. Configure webhook endpoints for credit purchase events')
    console.log('4. Update your UI to show credit balances and purchase options')
    console.log('')
    console.log('🎯 Credit system features now available:')
    console.log('- Credit balance tracking')
    console.log('- Credit package purchases')
    console.log('- Content unlocking with credits')
    console.log('- Author earnings from credit purchases')
    console.log('- Transaction history')
    console.log('- Revenue sharing (70% author, 30% platform)')

  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
